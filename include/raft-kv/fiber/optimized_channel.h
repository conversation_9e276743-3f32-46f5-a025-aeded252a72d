#pragma once

#include "channel.h"
#include <atomic>
#include <memory>

namespace monsoon
{
  /**
   * @brief 优化的Channel实现
   * @details 针对Raft-KV场景的性能优化版本
   */
  template <typename T>
  class OptimizedChannel : public Channel<T>
  {
  public:
    /**
     * @brief 构造函数
     * @param capacity 缓冲区容量
     * @param scheduler 调度器
     */
    OptimizedChannel(size_t capacity, Scheduler *scheduler = nullptr);

    /**
     * @brief 析构函数
     */
    ~OptimizedChannel() override = default;

    /**
     * @brief 优化的发送实现
     */
    ChannelResult send(const T &data, int timeoutMs = -1) override;
    ChannelResult send(T &&data, int timeoutMs = -1) override;

    /**
     * @brief 优化的接收实现
     */
    ChannelResult receive(T &data, int timeoutMs = -1) override;

    /**
     * @brief 批量发送
     * @param data 数据数组
     * @param count 数据数量
     * @param timeoutMs 超时时间
     * @return 实际发送的数量
     */
    size_t batchSend(const T *data, size_t count, int timeoutMs = -1);

    /**
     * @brief 批量接收
     * @param data 数据数组
     * @param maxCount 最大接收数量
     * @param timeoutMs 超时时间
     * @return 实际接收的数量
     */
    size_t batchReceive(T *data, size_t maxCount, int timeoutMs = -1);

    /**
     * @brief 获取性能统计
     */
    struct PerformanceStats
    {
      std::atomic<uint64_t> totalSent{0};
      std::atomic<uint64_t> totalReceived{0};
      std::atomic<uint64_t> sendTimeouts{0};
      std::atomic<uint64_t> receiveTimeouts{0};
      std::atomic<uint64_t> fastPathSends{0};    // 快速路径发送
      std::atomic<uint64_t> fastPathReceives{0}; // 快速路径接收
    };

    const PerformanceStats &getStats() const { return stats_; }
    void resetStats();

  private:
    PerformanceStats stats_;

    /**
     * @brief 快速路径发送（无锁）
     */
    bool tryFastSend(const T &data);

    /**
     * @brief 快速路径接收（无锁）
     */
    bool tryFastReceive(T &data);
  };

  /**
   * @brief 创建优化的Channel
   */
  template <typename T>
  std::shared_ptr<OptimizedChannel<T>> createOptimizedChannel(size_t capacity, Scheduler *scheduler = nullptr)
  {
    return std::make_shared<OptimizedChannel<T>>(capacity, scheduler);
  }

  /**
   * @brief Channel池，用于复用Channel对象
   */
  template <typename T>
  class ChannelPool
  {
  public:
    /**
     * @brief 获取Channel
     */
    std::shared_ptr<OptimizedChannel<T>> acquire(size_t capacity = 10);

    /**
     * @brief 归还Channel
     */
    void release(std::shared_ptr<OptimizedChannel<T>> channel);

    /**
     * @brief 获取池统计信息
     */
    struct PoolStats
    {
      size_t totalCreated = 0;
      size_t currentPooled = 0;
      size_t currentInUse = 0;
    };

    PoolStats getStats() const;

  private:
    mutable std::mutex mutex_;
    std::vector<std::shared_ptr<OptimizedChannel<T>>> pool_;
    PoolStats stats_;
  };

  /**
   * @brief 全局Channel池实例
   */
  template <typename T>
  ChannelPool<T> &getGlobalChannelPool()
  {
    static ChannelPool<T> pool;
    return pool;
  }

  /**
   * @brief Channel性能监控器
   */
  class ChannelMonitor
  {
  public:
    /**
     * @brief 注册Channel进行监控
     */
    template <typename T>
    void registerChannel(const std::string &name, std::shared_ptr<OptimizedChannel<T>> channel);

    /**
     * @brief 取消注册Channel
     */
    void unregisterChannel(const std::string &name);

    /**
     * @brief 打印所有Channel的性能统计
     */
    void printStats() const;

    /**
     * @brief 获取单例实例
     */
    static ChannelMonitor &getInstance();

  private:
    struct ChannelInfo
    {
      std::string name;
      std::function<void()> printStats;
    };

    mutable std::mutex mutex_;
    std::unordered_map<std::string, ChannelInfo> channels_;
  };

  /**
   * @brief Channel性能基准测试工具
   */
  class ChannelBenchmark
  {
  public:
    struct BenchmarkConfig
    {
      size_t messageCount = 10000;
      size_t channelCapacity = 100;
      int producerCount = 1;
      int consumerCount = 1;
      size_t messageSize = sizeof(int);
      bool useOptimizedChannel = true;
      bool useBatchOperations = false;
      size_t batchSize = 10;
    };

    struct BenchmarkResult
    {
      double throughputMsgPerSec = 0.0;
      double latencyMs = 0.0;
      double cpuUsagePercent = 0.0;
      size_t memoryUsageBytes = 0;
      bool success = false;
      std::string errorMessage;
    };

    /**
     * @brief 运行基准测试
     */
    static BenchmarkResult runBenchmark(const BenchmarkConfig &config);

    /**
     * @brief 比较不同配置的性能
     */
    static void compareConfigurations(const std::vector<BenchmarkConfig> &configs);
  };

} // namespace monsoon
