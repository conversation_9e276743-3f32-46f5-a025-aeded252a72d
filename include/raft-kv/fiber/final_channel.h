#pragma once

#include "raft-kv/fiber/fiber.h"
#include "raft-kv/fiber/scheduler.h"
#include <queue>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <atomic>
#include <chrono>
#include <functional>
#include <unordered_map>

namespace monsoon
{
  /**
   * @brief Channel结果枚举
   */
  enum class ChannelResult
  {
    SUCCESS = 0, // 成功
    TIMEOUT = 1, // 超时
    CLOSED = 2,  // Channel已关闭
    FULL = 3,    // Channel已满
    EMPTY = 4,   // Channel为空
    ERROR = 5    // 其他错误
  };

  /**
   * @brief 最终版本的Channel实现
   * @details 高性能、类型安全的协程Channel
   */
  template <typename T>
  class FinalChannel
  {
  public:
    using ptr = std::shared_ptr<FinalChannel<T>>;

    /**
     * @brief 构造函数
     * @param capacity 缓冲区容量，0表示无缓冲Channel
     * @param scheduler 调度器指针
     */
    explicit FinalChannel(size_t capacity = 0, Scheduler *scheduler = nullptr);

    /**
     * @brief 析构函数
     */
    ~FinalChannel();

    /**
     * @brief 发送数据
     * @param data 要发送的数据
     * @param timeoutMs 超时时间（毫秒），-1表示永不超时
     * @return 发送结果
     */
    ChannelResult send(const T &data, int timeoutMs = -1);
    ChannelResult send(T &&data, int timeoutMs = -1);

    /**
     * @brief 接收数据
     * @param data 接收数据的引用
     * @param timeoutMs 超时时间（毫秒），-1表示永不超时
     * @return 接收结果
     */
    ChannelResult receive(T &data, int timeoutMs = -1);

    /**
     * @brief 非阻塞发送
     * @param data 要发送的数据
     * @return 发送结果
     */
    ChannelResult trySend(const T &data);
    ChannelResult trySend(T &&data);

    /**
     * @brief 非阻塞接收
     * @param data 接收数据的引用
     * @return 接收结果
     */
    ChannelResult tryReceive(T &data);

    /**
     * @brief 关闭Channel
     */
    void close();

    /**
     * @brief 检查Channel是否已关闭
     */
    bool isClosed() const;

    /**
     * @brief 获取当前缓冲区大小
     */
    size_t size() const;

    /**
     * @brief 获取缓冲区容量
     */
    size_t capacity() const { return capacity_; }

    /**
     * @brief 检查缓冲区是否为空
     */
    bool empty() const;

    /**
     * @brief 检查缓冲区是否已满
     */
    bool full() const;

  private:
    const size_t capacity_;
    Scheduler *scheduler_;

    mutable std::mutex mutex_;
    std::queue<T> buffer_;
    std::atomic<bool> closed_{false};

    // 等待队列
    std::queue<Fiber::ptr> sendWaiters_;
    std::queue<Fiber::ptr> receiveWaiters_;

    // 性能统计
    std::atomic<uint64_t> totalSent_{0};
    std::atomic<uint64_t> totalReceived_{0};

    /**
     * @brief 内部发送实现
     */
    template <typename U>
    ChannelResult sendImpl(U &&data, int timeoutMs, bool blocking);

    /**
     * @brief 唤醒等待的协程
     */
    void wakeupWaiters();

    /**
     * @brief 检查超时
     */
    bool isTimeout(const std::chrono::steady_clock::time_point &start, int timeoutMs) const;
  };

  /**
   * @brief Channel选择器 - 类似Go的select语句
   */
  class FinalChannelSelector
  {
  public:
    /**
     * @brief 选择结果
     */
    struct SelectResult
    {
      int caseIndex = -1;   // 执行的case索引
      bool success = false; // 是否成功
      ChannelResult result = ChannelResult::ERROR;
      std::string errorMessage;
    };

    /**
     * @brief 操作类型
     */
    enum class OperationType
    {
      SEND,
      RECEIVE,
      DEFAULT
    };

    /**
     * @brief 添加发送case
     */
    template <typename T>
    void addSendCase(typename FinalChannel<T>::ptr channel, const T &data,
                     std::function<void(bool)> callback = nullptr);

    /**
     * @brief 添加接收case
     */
    template <typename T>
    void addReceiveCase(typename FinalChannel<T>::ptr channel, T *data,
                        std::function<void(const T &, bool)> callback = nullptr);

    /**
     * @brief 添加默认case
     */
    void addDefaultCase(std::function<void()> callback = nullptr);

    /**
     * @brief 执行选择操作
     * @param timeoutMs 超时时间
     * @return 选择结果
     */
    SelectResult select(int timeoutMs = -1);

    /**
     * @brief 清空所有case
     */
    void clear();

  private:
    struct SelectCase
    {
      OperationType type;
      void *channel;
      void *data;
      std::function<bool()> tryOperation;
      std::function<void()> callback;
    };

    std::vector<SelectCase> cases_;

    /**
     * @brief 尝试执行非阻塞操作
     */
    int tryExecuteNonBlocking();

    /**
     * @brief 检查超时
     */
    bool isTimeout(const std::chrono::steady_clock::time_point &start, int timeoutMs) const;
  };

  /**
   * @brief 创建Channel的工厂函数
   */
  template <typename T>
  typename FinalChannel<T>::ptr createFinalChannel(size_t capacity = 0, Scheduler *scheduler = nullptr)
  {
    return std::make_shared<FinalChannel<T>>(capacity, scheduler);
  }

  /**
   * @brief Channel管理器 - 管理所有Channel的生命周期
   */
  class ChannelManager
  {
  public:
    /**
     * @brief 获取单例实例
     */
    static ChannelManager &getInstance();

    /**
     * @brief 注册Channel
     */
    template <typename T>
    void registerChannel(const std::string &name, typename FinalChannel<T>::ptr channel);

    /**
     * @brief 注销Channel
     */
    void unregisterChannel(const std::string &name);

    /**
     * @brief 获取Channel
     */
    template <typename T>
    typename FinalChannel<T>::ptr getChannel(const std::string &name);

    /**
     * @brief 关闭所有Channel
     */
    void closeAll();

    /**
     * @brief 获取统计信息
     */
    struct Stats
    {
      size_t totalChannels = 0;
      size_t activeChannels = 0;
      uint64_t totalMessagesSent = 0;
      uint64_t totalMessagesReceived = 0;
    };

    Stats getStats() const;

  private:
    mutable std::mutex mutex_;
    std::unordered_map<std::string, std::shared_ptr<void>> channels_;

    ChannelManager() = default;
  };

  /**
   * @brief 性能优化的Channel实现
   */
  template <typename T>
  class OptimizedChannel : public FinalChannel<T>
  {
  public:
    explicit OptimizedChannel(size_t capacity = 0, Scheduler *scheduler = nullptr)
        : FinalChannel<T>(capacity, scheduler) {}

    /**
     * @brief 快速路径发送（针对常见场景优化）
     */
    ChannelResult fastSend(const T &data);

    /**
     * @brief 快速路径接收（针对常见场景优化）
     */
    ChannelResult fastReceive(T &data);

    /**
     * @brief 批量操作
     */
    size_t batchSend(const std::vector<T> &data, int timeoutMs = -1);
    std::vector<T> batchReceive(size_t maxCount, int timeoutMs = -1);
  };

  /**
   * @brief 创建优化Channel
   */
  template <typename T>
  std::shared_ptr<OptimizedChannel<T>> createOptimizedChannel(size_t capacity = 0)
  {
    return std::make_shared<OptimizedChannel<T>>(capacity);
  }

} // namespace monsoon
