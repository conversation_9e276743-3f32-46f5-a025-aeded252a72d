#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <vector>
#include <chrono>

using namespace monsoon;

/**
 * @brief 演示Channel替代LockQueue的示例
 * @details 展示如何使用Channel简化协程间通信，提供更好的编程体验
 */

// 模拟一个简单的任务结构
struct Task
{
    int id;
    std::string data;
    std::chrono::steady_clock::time_point timestamp;

    Task() = default;
    Task(int i, const std::string &d)
        : id(i), data(d), timestamp(std::chrono::steady_clock::now()) {}
};

// 使用Channel实现生产者-消费者模式
void demonstrateProducerConsumer()
{
    std::cout << "=== Channel生产者-消费者模式演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(3, true, "ProducerConsumer");
    ioManager->start();

    // 创建任务Channel
    auto taskChannel = createChannel<Task>(10);
    auto resultChannel = createChannel<std::string>(10);

    // 生产者协程
    ioManager->scheduler([taskChannel]()
                         {
        for (int i = 1; i <= 5; ++i) {
            Task task(i, "Task_" + std::to_string(i));
            auto result = taskChannel->send(task);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "生产者: 发送任务 " << task.id << std::endl;
            }
            
            // 模拟生产间隔
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 发送结束信号
        taskChannel->close();
        std::cout << "生产者: 完成所有任务生产" << std::endl; });

    // 消费者协程1
    ioManager->scheduler([taskChannel, resultChannel]()
                         {
        while (true) {
            Task task;
            auto result = taskChannel->receive(task);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "消费者1: 处理任务 " << task.id << std::endl;
                
                // 模拟任务处理
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
                
                // 发送处理结果
                std::string resultMsg = "任务" + std::to_string(task.id) + "由消费者1完成";
                resultChannel->send(resultMsg);
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "消费者1: 任务Channel已关闭，退出" << std::endl;
                break;
            }
        } });

    // 消费者协程2
    ioManager->scheduler([taskChannel, resultChannel]()
                         {
        while (true) {
            Task task;
            auto result = taskChannel->receive(task);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "消费者2: 处理任务 " << task.id << std::endl;
                
                // 模拟任务处理
                std::this_thread::sleep_for(std::chrono::milliseconds(80));
                
                // 发送处理结果
                std::string resultMsg = "任务" + std::to_string(task.id) + "由消费者2完成";
                resultChannel->send(resultMsg);
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "消费者2: 任务Channel已关闭，退出" << std::endl;
                break;
            }
        } });

    // 结果收集协程
    ioManager->scheduler([resultChannel]()
                         {
        int resultCount = 0;
        while (resultCount < 5) { // 期望收到5个结果
            std::string result;
            auto status = resultChannel->receive(result, 2000); // 2秒超时
            
            if (status == ChannelResult::SUCCESS) {
                std::cout << "结果收集器: " << result << std::endl;
                resultCount++;
            } else if (status == ChannelResult::TIMEOUT) {
                std::cout << "结果收集器: 接收超时" << std::endl;
                break;
            }
        }
        std::cout << "结果收集器: 收集完成，共收到 " << resultCount << " 个结果" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    ioManager->stop();
    std::cout << "生产者-消费者演示完成\n"
              << std::endl;
}

// 演示Channel的管道模式
void demonstratePipeline()
{
    std::cout << "=== Channel管道模式演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(4, true, "Pipeline");
    ioManager->start();

    // 创建管道的各个阶段
    auto inputChannel = createChannel<int>(5);
    auto stage1Channel = createChannel<int>(5);
    auto stage2Channel = createChannel<std::string>(5);
    auto outputChannel = createChannel<std::string>(5);

    // 数据输入阶段
    ioManager->scheduler([inputChannel]()
                         {
        for (int i = 1; i <= 10; ++i) {
            inputChannel->send(i);
            std::cout << "输入: " << i << std::endl;
        }
        inputChannel->close(); });

    // 处理阶段1：数字平方
    ioManager->scheduler([inputChannel, stage1Channel]()
                         {
        while (true) {
            int data;
            auto result = inputChannel->receive(data);
            
            if (result == ChannelResult::SUCCESS) {
                int squared = data * data;
                stage1Channel->send(squared);
                std::cout << "阶段1: " << data << " -> " << squared << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                stage1Channel->close();
                break;
            }
        } });

    // 处理阶段2：数字转字符串并添加前缀
    ioManager->scheduler([stage1Channel, stage2Channel]()
                         {
        while (true) {
            int data;
            auto result = stage1Channel->receive(data);
            
            if (result == ChannelResult::SUCCESS) {
                std::string formatted = "结果:" + std::to_string(data);
                stage2Channel->send(formatted);
                std::cout << "阶段2: " << data << " -> " << formatted << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                stage2Channel->close();
                break;
            }
        } });

    // 输出阶段
    ioManager->scheduler([stage2Channel]()
                         {
        while (true) {
            std::string data;
            auto result = stage2Channel->receive(data);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "输出: " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "管道处理完成" << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "管道模式演示完成\n"
              << std::endl;
}

// 演示Channel与现有LockQueue的性能对比
void demonstratePerformanceComparison()
{
    std::cout << "=== Channel vs LockQueue 性能对比 ===" << std::endl;

    const int messageCount = 1000;

    // 测试LockQueue性能
    auto start = std::chrono::high_resolution_clock::now();
    {
        LockQueue<int> lockQueue;

        std::thread producer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                lockQueue.Push(i);
            } });

        std::thread consumer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                int data = lockQueue.Pop();
                (void)data; // 避免未使用变量警告
            } });

        producer.join();
        consumer.join();
    }
    auto lockQueueTime = std::chrono::duration_cast<std::chrono::microseconds>(
                             std::chrono::high_resolution_clock::now() - start)
                             .count();

    // 测试Channel性能
    start = std::chrono::high_resolution_clock::now();
    {
        auto ioManager = std::make_unique<IOManager>(2, true, "PerfTest");
        ioManager->start();

        auto channel = createChannel<int>(100);

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                channel->send(i);
            } });

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                int data;
                channel->receive(data);
            } });

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        ioManager->stop();
    }
    auto channelTime = std::chrono::duration_cast<std::chrono::microseconds>(
                           std::chrono::high_resolution_clock::now() - start)
                           .count();

    std::cout << "LockQueue 处理 " << messageCount << " 消息耗时: " << lockQueueTime << " 微秒" << std::endl;
    std::cout << "Channel 处理 " << messageCount << " 消息耗时: " << channelTime << " 微秒" << std::endl;
    std::cout << "性能对比完成\n"
              << std::endl;
}

int main()
{
    std::cout << "Channel使用示例演示" << std::endl;
    std::cout << "===================" << std::endl;

    try
    {
        demonstrateProducerConsumer();
        demonstratePipeline();
        demonstratePerformanceComparison();

        std::cout << "所有演示完成！" << std::endl;
        std::cout << "\nChannel的优势:" << std::endl;
        std::cout << "1. 类型安全的协程间通信" << std::endl;
        std::cout << "2. 自动的协程调度和唤醒" << std::endl;
        std::cout << "3. 支持超时和非阻塞操作" << std::endl;
        std::cout << "4. 更清晰的编程模型" << std::endl;
        std::cout << "5. 与协程库深度集成" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
