#include "raft-kv/fiber/monsoon.h"
#include <iostream>
#include <string>

using namespace monsoon;

/**
 * @brief 最基本的Channel测试，不使用协程
 * @details 测试Channel的基本功能，如创建、状态查询等
 */

void testChannelCreation()
{
    std::cout << "=== 测试Channel创建 ===" << std::endl;
    
    // 测试创建不同类型的Channel
    auto intChannel = makeChannel<int>(5);
    auto stringChannel = makeChannel<std::string>(10);
    
    std::cout << "创建int Channel成功，容量: " << intChannel->capacity() << std::endl;
    std::cout << "创建string Channel成功，容量: " << stringChannel->capacity() << std::endl;
    
    std::cout << "Channel创建测试通过\n" << std::endl;
}

void testChannelStatus()
{
    std::cout << "=== 测试Channel状态 ===" << std::endl;
    
    auto channel = makeChannel<int>(3);
    
    std::cout << "初始状态:" << std::endl;
    std::cout << "  容量: " << channel->capacity() << std::endl;
    std::cout << "  大小: " << channel->size() << std::endl;
    std::cout << "  是否空: " << (channel->empty() ? "是" : "否") << std::endl;
    std::cout << "  是否满: " << (channel->full() ? "是" : "否") << std::endl;
    std::cout << "  是否关闭: " << (channel->isClosed() ? "是" : "否") << std::endl;
    
    std::cout << "Channel状态测试通过\n" << std::endl;
}

void testNonBlockingOperations()
{
    std::cout << "=== 测试非阻塞操作 ===" << std::endl;
    
    auto channel = makeChannel<int>(2);
    
    // 测试非阻塞发送
    auto result = channel->trySend(1);
    std::cout << "发送1: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    result = channel->trySend(2);
    std::cout << "发送2: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    // 缓冲区应该满了
    result = channel->trySend(3);
    std::cout << "发送3: " << (result == ChannelResult::WOULD_BLOCK ? "缓冲区满" : "意外成功") << std::endl;
    
    std::cout << "当前Channel状态 - 大小: " << channel->size() 
              << ", 是否满: " << (channel->full() ? "是" : "否") << std::endl;
    
    // 测试非阻塞接收
    int data;
    result = channel->tryReceive(data);
    if (result == ChannelResult::SUCCESS) {
        std::cout << "接收到: " << data << std::endl;
    }
    
    result = channel->tryReceive(data);
    if (result == ChannelResult::SUCCESS) {
        std::cout << "接收到: " << data << std::endl;
    }
    
    // 现在应该空了
    result = channel->tryReceive(data);
    std::cout << "从空Channel接收: " << (result == ChannelResult::WOULD_BLOCK ? "缓冲区空" : "意外成功") << std::endl;
    
    std::cout << "非阻塞操作测试通过\n" << std::endl;
}

void testChannelClose()
{
    std::cout << "=== 测试Channel关闭 ===" << std::endl;
    
    auto channel = makeChannel<int>(2);
    
    // 添加一些数据
    channel->trySend(1);
    channel->trySend(2);
    
    std::cout << "关闭前 - 大小: " << channel->size() 
              << ", 是否关闭: " << (channel->isClosed() ? "是" : "否") << std::endl;
    
    // 关闭Channel
    channel->close();
    
    std::cout << "关闭后 - 是否关闭: " << (channel->isClosed() ? "是" : "否") << std::endl;
    
    // 尝试发送到已关闭的Channel
    auto result = channel->trySend(3);
    std::cout << "向已关闭Channel发送: " << (result == ChannelResult::CLOSED ? "正确拒绝" : "意外成功") << std::endl;
    
    // 仍然可以接收已缓冲的数据
    int data;
    result = channel->tryReceive(data);
    if (result == ChannelResult::SUCCESS) {
        std::cout << "从已关闭Channel接收缓冲数据: " << data << std::endl;
    }
    
    std::cout << "Channel关闭测试通过\n" << std::endl;
}

void testChannelManager()
{
    std::cout << "=== 测试ChannelManager ===" << std::endl;
    
    auto manager = ChannelManager::getInstance();
    std::cout << "获取ChannelManager单例成功" << std::endl;
    
    // 使用ChannelManager创建Channel
    auto channel1 = manager->createChannel<int>(5);
    auto channel2 = manager->createChannel<std::string>(10);
    
    std::cout << "通过ChannelManager创建Channel成功" << std::endl;
    std::cout << "int Channel容量: " << channel1->capacity() << std::endl;
    std::cout << "string Channel容量: " << channel2->capacity() << std::endl;
    
    // 测试便捷函数
    auto channel3 = createChannel<int>(3);
    std::cout << "通过便捷函数创建Channel成功，容量: " << channel3->capacity() << std::endl;
    
    std::cout << "ChannelManager测试通过\n" << std::endl;
}

void testStringChannel()
{
    std::cout << "=== 测试字符串Channel ===" << std::endl;
    
    auto channel = makeChannel<std::string>(3);
    
    // 发送字符串
    auto result = channel->trySend(std::string("Hello"));
    std::cout << "发送'Hello': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    result = channel->trySend(std::string("World"));
    std::cout << "发送'World': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    // 接收字符串
    std::string data;
    result = channel->tryReceive(data);
    if (result == ChannelResult::SUCCESS) {
        std::cout << "接收到: '" << data << "'" << std::endl;
    }
    
    result = channel->tryReceive(data);
    if (result == ChannelResult::SUCCESS) {
        std::cout << "接收到: '" << data << "'" << std::endl;
    }
    
    std::cout << "字符串Channel测试通过\n" << std::endl;
}

void testChannelCapacities()
{
    std::cout << "=== 测试不同容量的Channel ===" << std::endl;
    
    // 无缓冲Channel
    auto unbuffered = makeChannel<int>(0);
    std::cout << "无缓冲Channel - 容量: " << unbuffered->capacity() 
              << ", 是否满: " << (unbuffered->full() ? "是" : "否") << std::endl;
    
    // 小缓冲Channel
    auto small = makeChannel<int>(1);
    small->trySend(42);
    std::cout << "小缓冲Channel - 容量: " << small->capacity() 
              << ", 大小: " << small->size()
              << ", 是否满: " << (small->full() ? "是" : "否") << std::endl;
    
    // 大缓冲Channel
    auto large = makeChannel<int>(100);
    std::cout << "大缓冲Channel - 容量: " << large->capacity() 
              << ", 是否空: " << (large->empty() ? "是" : "否") << std::endl;
    
    std::cout << "不同容量Channel测试通过\n" << std::endl;
}

int main()
{
    std::cout << "基本Channel功能测试" << std::endl;
    std::cout << "==================" << std::endl;
    
    try {
        testChannelCreation();
        testChannelStatus();
        testNonBlockingOperations();
        testChannelClose();
        testChannelManager();
        testStringChannel();
        testChannelCapacities();
        
        std::cout << "所有基本功能测试通过！" << std::endl;
        std::cout << "\n测试总结:" << std::endl;
        std::cout << "✓ Channel创建和销毁" << std::endl;
        std::cout << "✓ 状态查询功能" << std::endl;
        std::cout << "✓ 非阻塞发送和接收" << std::endl;
        std::cout << "✓ Channel关闭机制" << std::endl;
        std::cout << "✓ ChannelManager管理" << std::endl;
        std::cout << "✓ 多种数据类型支持" << std::endl;
        std::cout << "✓ 不同缓冲区大小" << std::endl;
        
        std::cout << "\n注意: 协程相关功能需要在协程环境中测试" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
