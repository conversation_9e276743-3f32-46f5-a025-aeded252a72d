#include "raft-kv/fiber/monsoon.h"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>

using namespace monsoon;

/**
 * @brief 简单的协程Channel测试
 * @details 测试Channel在协程环境中的基本功能
 */

void testBasicFiberChannel()
{
    std::cout << "=== 协程Channel基本测试 ===" << std::endl;
    
    // 创建IOManager，使用较少的线程避免问题
    auto ioManager = std::make_unique<IOManager>(1, false, "ChannelTest");
    
    auto channel = createChannel<int>(2);
    
    std::cout << "IOManager创建成功，开始启动..." << std::endl;
    ioManager->start();
    
    // 简单的发送协程
    ioManager->scheduler([channel]() {
        std::cout << "发送协程开始" << std::endl;
        for (int i = 1; i <= 3; ++i) {
            auto result = channel->send(i, 1000); // 1秒超时
            if (result == ChannelResult::SUCCESS) {
                std::cout << "发送成功: " << i << std::endl;
            } else {
                std::cout << "发送失败: " << i << ", 结果: " << (int)result << std::endl;
            }
            // 让出一下执行权
            Fiber::GetThis()->yield();
        }
        channel->close();
        std::cout << "发送协程结束" << std::endl;
    });
    
    // 简单的接收协程
    ioManager->scheduler([channel]() {
        std::cout << "接收协程开始" << std::endl;
        while (true) {
            int data;
            auto result = channel->receive(data, 2000); // 2秒超时
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收成功: " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "Channel已关闭，接收协程退出" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "接收超时" << std::endl;
                break;
            } else {
                std::cout << "接收失败，结果: " << (int)result << std::endl;
                break;
            }
            
            // 让出一下执行权
            Fiber::GetThis()->yield();
        }
        std::cout << "接收协程结束" << std::endl;
    });
    
    // 等待协程执行完成
    std::this_thread::sleep_for(std::chrono::milliseconds(3000));
    
    std::cout << "停止IOManager..." << std::endl;
    ioManager->stop();
    
    std::cout << "协程Channel基本测试完成\n" << std::endl;
}

void testNonBlockingInFiber()
{
    std::cout << "=== 协程中的非阻塞操作测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(1, false, "NonBlockingTest");
    auto channel = createChannel<std::string>(1);
    
    ioManager->start();
    
    ioManager->scheduler([channel]() {
        std::cout << "非阻塞测试协程开始" << std::endl;
        
        // 测试非阻塞发送
        auto result = channel->trySend(std::string("Hello"));
        std::cout << "非阻塞发送'Hello': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        // 缓冲区满了，再次发送应该失败
        result = channel->trySend(std::string("World"));
        std::cout << "非阻塞发送'World': " << (result == ChannelResult::WOULD_BLOCK ? "缓冲区满" : "意外成功") << std::endl;
        
        // 非阻塞接收
        std::string data;
        result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "非阻塞接收: '" << data << "'" << std::endl;
        }
        
        // 现在应该可以发送了
        result = channel->trySend(std::string("World"));
        std::cout << "再次非阻塞发送'World': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        // 接收剩余数据
        result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "接收剩余数据: '" << data << "'" << std::endl;
        }
        
        std::cout << "非阻塞测试协程结束" << std::endl;
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    
    std::cout << "协程中的非阻塞操作测试完成\n" << std::endl;
}

void testChannelTimeout()
{
    std::cout << "=== Channel超时测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(1, false, "TimeoutTest");
    auto channel = createChannel<int>(0); // 无缓冲Channel
    
    ioManager->start();
    
    ioManager->scheduler([channel]() {
        std::cout << "超时测试协程开始" << std::endl;
        
        // 测试接收超时
        auto start = std::chrono::steady_clock::now();
        int data;
        auto result = channel->receive(data, 500); // 500ms超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        std::cout << "接收超时测试 - 结果: " 
                  << (result == ChannelResult::TIMEOUT ? "超时" : "意外成功")
                  << ", 耗时: " << elapsed << "ms" << std::endl;
        
        // 测试发送超时
        start = std::chrono::steady_clock::now();
        result = channel->send(42, 500); // 500ms超时
        elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        std::cout << "发送超时测试 - 结果: " 
                  << (result == ChannelResult::TIMEOUT ? "超时" : "意外成功")
                  << ", 耗时: " << elapsed << "ms" << std::endl;
        
        std::cout << "超时测试协程结束" << std::endl;
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    ioManager->stop();
    
    std::cout << "Channel超时测试完成\n" << std::endl;
}

int main()
{
    std::cout << "协程Channel功能测试" << std::endl;
    std::cout << "==================" << std::endl;
    
    try {
        testBasicFiberChannel();
        testNonBlockingInFiber();
        testChannelTimeout();
        
        std::cout << "所有协程Channel测试完成！" << std::endl;
        std::cout << "\n测试总结:" << std::endl;
        std::cout << "✓ 协程间Channel通信" << std::endl;
        std::cout << "✓ 协程中的非阻塞操作" << std::endl;
        std::cout << "✓ Channel超时机制" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
