#include "raft-kv/fiber/monsoon.h"
#include <iostream>
#include <chrono>
#include <thread>

using namespace monsoon;

/**
 * @brief 最简单的协程Channel测试
 */

void testSimpleFiberCommunication()
{
    std::cout << "=== 简单协程通信测试 ===" << std::endl;
    
    // 创建IOManager（会自动启动）
    auto ioManager = std::make_unique<IOManager>(1, false, "SimpleTest");
    auto channel = createChannel<int>(1);
    
    std::cout << "开始协程通信测试..." << std::endl;
    
    // 发送协程
    ioManager->scheduler([channel]() {
        std::cout << "发送协程: 发送数据 42" << std::endl;
        auto result = channel->send(42);
        std::cout << "发送协程: 发送结果 " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    });
    
    // 接收协程
    ioManager->scheduler([channel]() {
        std::cout << "接收协程: 等待接收数据" << std::endl;
        int data;
        auto result = channel->receive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "接收协程: 接收到数据 " << data << std::endl;
        } else {
            std::cout << "接收协程: 接收失败" << std::endl;
        }
    });
    
    // 等待协程完成
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    std::cout << "停止IOManager..." << std::endl;
    ioManager->stop();
    std::cout << "简单协程通信测试完成\n" << std::endl;
}

void testProducerConsumer()
{
    std::cout << "=== 生产者消费者测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(2, false, "ProducerConsumer");
    auto channel = createChannel<int>(3);
    
    // 生产者
    ioManager->scheduler([channel]() {
        std::cout << "生产者: 开始生产" << std::endl;
        for (int i = 1; i <= 5; ++i) {
            std::cout << "生产者: 生产 " << i << std::endl;
            channel->send(i);
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
        }
        channel->close();
        std::cout << "生产者: 生产完成" << std::endl;
    });
    
    // 消费者
    ioManager->scheduler([channel]() {
        std::cout << "消费者: 开始消费" << std::endl;
        while (true) {
            int data;
            auto result = channel->receive(data, 3000); // 3秒超时
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "消费者: 消费 " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "消费者: Channel已关闭，退出" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "消费者: 接收超时" << std::endl;
                break;
            } else {
                std::cout << "消费者: 接收失败" << std::endl;
                break;
            }
        }
        std::cout << "消费者: 消费完成" << std::endl;
    });
    
    // 等待足够长的时间
    std::this_thread::sleep_for(std::chrono::milliseconds(3000));
    
    ioManager->stop();
    std::cout << "生产者消费者测试完成\n" << std::endl;
}

void testNonBlockingInFiber()
{
    std::cout << "=== 协程中非阻塞操作测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(1, false, "NonBlocking");
    auto channel = createChannel<std::string>(2);
    
    ioManager->scheduler([channel]() {
        std::cout << "测试协程: 开始非阻塞操作" << std::endl;
        
        // 非阻塞发送
        auto result = channel->trySend(std::string("Hello"));
        std::cout << "非阻塞发送 'Hello': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        result = channel->trySend(std::string("World"));
        std::cout << "非阻塞发送 'World': " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        // 缓冲区满了
        result = channel->trySend(std::string("Full"));
        std::cout << "非阻塞发送 'Full': " << (result == ChannelResult::WOULD_BLOCK ? "缓冲区满" : "意外成功") << std::endl;
        
        // 非阻塞接收
        std::string data;
        result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "非阻塞接收: '" << data << "'" << std::endl;
        }
        
        result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "非阻塞接收: '" << data << "'" << std::endl;
        }
        
        // 现在应该空了
        result = channel->tryReceive(data);
        std::cout << "从空Channel接收: " << (result == ChannelResult::WOULD_BLOCK ? "缓冲区空" : "意外成功") << std::endl;
        
        std::cout << "测试协程: 非阻塞操作完成" << std::endl;
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "协程中非阻塞操作测试完成\n" << std::endl;
}

void testChannelClose()
{
    std::cout << "=== Channel关闭测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(2, false, "CloseTest");
    auto channel = createChannel<int>(2);
    
    // 发送一些数据然后关闭
    ioManager->scheduler([channel]() {
        std::cout << "发送协程: 发送数据并关闭Channel" << std::endl;
        channel->send(1);
        channel->send(2);
        std::cout << "发送协程: 关闭Channel" << std::endl;
        channel->close();
        
        // 尝试再发送
        auto result = channel->send(3);
        std::cout << "发送协程: 关闭后发送结果 " << (result == ChannelResult::CLOSED ? "正确拒绝" : "意外成功") << std::endl;
    });
    
    // 接收所有数据
    ioManager->scheduler([channel]() {
        std::cout << "接收协程: 开始接收" << std::endl;
        while (true) {
            int data;
            auto result = channel->receive(data, 1000);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收协程: 接收到 " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "接收协程: Channel已关闭" << std::endl;
                break;
            } else {
                std::cout << "接收协程: 接收失败或超时" << std::endl;
                break;
            }
        }
        std::cout << "接收协程: 完成" << std::endl;
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    ioManager->stop();
    std::cout << "Channel关闭测试完成\n" << std::endl;
}

int main()
{
    std::cout << "简单协程Channel测试" << std::endl;
    std::cout << "==================" << std::endl;
    
    try {
        testSimpleFiberCommunication();
        testNonBlockingInFiber();
        testChannelClose();
        testProducerConsumer();
        
        std::cout << "所有测试完成！" << std::endl;
        std::cout << "\n✅ Channel协程通信功能正常工作" << std::endl;
        std::cout << "✅ 非阻塞操作正常" << std::endl;
        std::cout << "✅ Channel关闭机制正常" << std::endl;
        std::cout << "✅ 生产者消费者模式正常" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
