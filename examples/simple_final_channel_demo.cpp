#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>

using namespace monsoon;

/**
 * @brief 简化的最终Channel演示
 */

void testBasicChannel()
{
    std::cout << "=== 基本Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "BasicTest");

    // 使用原有的Channel实现
    auto ch = createChannel<int>(3);

    // 发送方
    ioManager->scheduler([ch]()
                         {
        std::cout << "发送方: 开始发送" << std::endl;
        for (int i = 1; i <= 5; ++i) {
            auto result = ch->send(i * 10);
            std::cout << "发送 " << (i * 10) << ": " 
                      << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
        ch->close(); });

    // 接收方
    ioManager->scheduler([ch]()
                         {
        std::cout << "接收方: 开始接收" << std::endl;
        while (true) {
            int data;
            auto result = ch->receive(data, 1000);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收到: " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "Channel已关闭" << std::endl;
                break;
            } else {
                std::cout << "接收超时或失败" << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(800));
    ioManager->stop();
    std::cout << "基本Channel测试完成\n"
              << std::endl;
}

void testChannelSelector()
{
    std::cout << "=== ChannelSelector测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "SelectorTest");

    auto ch1 = createChannel<int>(2);
    auto ch2 = createChannel<std::string>(2);

    // 预先发送数据
    ch1->trySend(42);
    ch2->trySend("Hello");

    ioManager->scheduler([ch1, ch2]()
                         {
        std::cout << "开始ChannelSelector测试" << std::endl;
        
        ChannelSelector selector;
        
        int intData;
        std::string stringData;
        
        selector.addReceiveCase<int>(ch1, &intData, [](const int& data, bool success) {
            std::cout << "从ch1接收: " << data << std::endl;
        });
        
        selector.addReceiveCase<std::string>(ch2, &stringData, [](const std::string& data, bool success) {
            std::cout << "从ch2接收: " << data << std::endl;
        });
        
        selector.addDefaultCase([]() {
            std::cout << "执行默认case" << std::endl;
        });
        
        auto result = selector.select(100);
        std::cout << "选择结果: case=" << result.caseIndex 
                  << ", 成功=" << result.success << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    ioManager->stop();
    std::cout << "ChannelSelector测试完成\n"
              << std::endl;
}

void testApplyMsgChannel()
{
    std::cout << "=== ApplyMsg Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "ApplyMsgTest");

    auto applyChan = createChannel<ApplyMsg>(5);

    // 模拟Raft发送
    ioManager->scheduler([applyChan]()
                         {
        std::cout << "Raft: 发送ApplyMsg" << std::endl;
        
        ApplyMsg msg;
        msg.CommandValid = true;
        msg.CommandIndex = 1;
        msg.Command = "PUT key1 value1";
        
        auto result = applyChan->send(msg);
        std::cout << "Raft: 发送结果 " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        applyChan->close(); });

    // 模拟KvServer接收
    ioManager->scheduler([applyChan]()
                         {
        std::cout << "KvServer: 接收ApplyMsg" << std::endl;

        ApplyMsg msg;
        auto result = applyChan->receive(msg, 1000);
        
        if (result == ChannelResult::SUCCESS) {
            std::cout << "KvServer: 处理消息 " << msg.CommandIndex 
                      << " - " << msg.Command << std::endl;
        } else {
            std::cout << "KvServer: 接收失败" << std::endl;
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    ioManager->stop();
    std::cout << "ApplyMsg Channel测试完成\n"
              << std::endl;
}

void testOpChannel()
{
    std::cout << "=== Op Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "OpTest");

    auto opChan = createChannel<Op>(3);

    // 发送Op
    ioManager->scheduler([opChan]()
                         {
        std::cout << "发送Op操作" << std::endl;
        
        Op op;
        op.Operation = "PUT";
        op.Key = "test_key";
        op.Value = "test_value";
        op.ClientId = "client_1";
        op.RequestId = 1;
        
        auto result = opChan->send(op);
        std::cout << "发送Op结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl; });

    // 接收Op
    ioManager->scheduler([opChan]()
                         {
        std::cout << "接收Op操作" << std::endl;
        
        Op op;
        auto result = opChan->receive(op, 1000);
        
        if (result == ChannelResult::SUCCESS) {
            std::cout << "接收到Op: " << op.Operation << " " 
                      << op.Key << "=" << op.Value << std::endl;
        } else {
            std::cout << "接收Op失败" << std::endl;
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    ioManager->stop();
    std::cout << "Op Channel测试完成\n"
              << std::endl;
}

void testPerformance()
{
    std::cout << "=== 性能测试 ===" << std::endl;

    const int messageCount = 1000;

    // 测试LockQueue
    auto start = std::chrono::high_resolution_clock::now();
    {
        LockQueue<int> lockQueue;

        std::thread producer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                lockQueue.Push(i);
            } });

        std::thread consumer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                int data = lockQueue.Pop();
                (void)data;
            } });

        producer.join();
        consumer.join();
    }
    auto lockQueueTime = std::chrono::duration_cast<std::chrono::microseconds>(
                             std::chrono::high_resolution_clock::now() - start)
                             .count();

    // 测试Channel
    start = std::chrono::high_resolution_clock::now();
    {
        auto ioManager = std::make_unique<IOManager>(2, false, "PerfTest");
        auto channel = createChannel<int>(100);

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                channel->send(i);
            } });

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                int data;
                channel->receive(data);
            } });

        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        ioManager->stop();
    }
    auto channelTime = std::chrono::duration_cast<std::chrono::microseconds>(
                           std::chrono::high_resolution_clock::now() - start)
                           .count();

    std::cout << "处理 " << messageCount << " 个消息:" << std::endl;
    std::cout << "LockQueue: " << lockQueueTime << " 微秒" << std::endl;
    std::cout << "Channel: " << channelTime << " 微秒" << std::endl;

    if (channelTime < lockQueueTime)
    {
        std::cout << "Channel性能提升: "
                  << ((double)(lockQueueTime - channelTime) / lockQueueTime * 100) << "%" << std::endl;
    }
    else
    {
        std::cout << "LockQueue性能更好: "
                  << ((double)(channelTime - lockQueueTime) / channelTime * 100) << "%" << std::endl;
    }

    std::cout << "性能测试完成\n"
              << std::endl;
}

int main()
{
    std::cout << "🚀 简化Channel系统演示" << std::endl;
    std::cout << "=====================" << std::endl;

    try
    {
        testBasicChannel();
        testChannelSelector();
        testApplyMsgChannel();
        testOpChannel();
        testPerformance();

        std::cout << "🎉 所有测试完成！" << std::endl;
        std::cout << "\n✅ 基本Channel操作正常" << std::endl;
        std::cout << "✅ ChannelSelector功能正常" << std::endl;
        std::cout << "✅ ApplyMsg Channel正常" << std::endl;
        std::cout << "✅ Op Channel正常" << std::endl;
        std::cout << "✅ 性能测试完成" << std::endl;
        std::cout << "\n🎯 Channel系统集成成功！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
