#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <unordered_map>

using namespace monsoon;

/**
 * @brief 测试waitApplyCh从LockQueue迁移到Channel的功能
 * @details 模拟KvServer中等待特定raftIndex操作完成的机制
 */

void testWaitApplyChChannel()
{
    std::cout << "=== waitApplyCh Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(3, false, "WaitApplyChTest");

    // 模拟waitApplyChChannel
    std::unordered_map<int, Channel<Op>::ptr> waitApplyChChannel;

    // 模拟客户端请求等待特定raftIndex的操作完成
    ioManager->scheduler([&waitApplyChChannel]()
                         {
        std::cout << "客户端协程: 发起请求，等待raftIndex=100的操作完成" << std::endl;
        
        // 创建等待Channel
        int raftIndex = 100;
        auto waitChannel = createChannel<Op>(1);
        waitApplyChChannel[raftIndex] = waitChannel;
        
        // 等待操作完成
        Op result;
        auto channelResult = waitChannel->receive(result, 3000); // 3秒超时
        
        if (channelResult == ChannelResult::SUCCESS) {
            std::cout << "客户端协程: 收到操作结果 - 操作=" << result.Operation 
                      << ", 键=" << result.Key << ", 值=" << result.Value << std::endl;
        } else if (channelResult == ChannelResult::TIMEOUT) {
            std::cout << "客户端协程: 等待超时" << std::endl;
        } else {
            std::cout << "客户端协程: 等待失败，结果=" << (int)channelResult << std::endl;
        }
        
        // 清理
        waitApplyChChannel.erase(raftIndex);
        std::cout << "客户端协程: 完成等待，清理资源" << std::endl; });

    // 模拟Raft应用消息，通知等待的客户端
    ioManager->scheduler([&waitApplyChChannel]()
                         {
        std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 模拟处理延迟
        
        std::cout << "Raft协程: 开始处理raftIndex=100的操作" << std::endl;
        
        int raftIndex = 100;
        if (waitApplyChChannel.find(raftIndex) != waitApplyChChannel.end()) {
            Op op;
            op.Operation = "Put";
            op.Key = "test_key";
            op.Value = "test_value";
            op.ClientId = "client_123";
            op.RequestId = 1;
            
            auto result = waitApplyChChannel[raftIndex]->send(op);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "Raft协程: 成功发送操作结果到等待Channel" << std::endl;
            } else {
                std::cout << "Raft协程: 发送操作结果失败，结果=" << (int)result << std::endl;
            }
        } else {
            std::cout << "Raft协程: 没有找到等待raftIndex=100的Channel" << std::endl;
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    ioManager->stop();
    std::cout << "waitApplyCh Channel测试完成\n"
              << std::endl;
}

void testMultipleWaitChannels()
{
    std::cout << "=== 多个等待Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(5, false, "MultiWaitTest");

    std::unordered_map<int, Channel<Op>::ptr> waitApplyChChannel;
    std::atomic<int> completedRequests{0};

    // 模拟多个客户端请求
    for (int clientId = 1; clientId <= 3; ++clientId)
    {
        ioManager->scheduler([&waitApplyChChannel, &completedRequests, clientId]()
                             {
            int raftIndex = 200 + clientId;
            std::cout << "客户端" << clientId << ": 等待raftIndex=" << raftIndex << std::endl;
            
            auto waitChannel = createChannel<Op>(1);
            waitApplyChChannel[raftIndex] = waitChannel;
            
            Op result;
            auto channelResult = waitChannel->receive(result, 2000);
            
            if (channelResult == ChannelResult::SUCCESS) {
                std::cout << "客户端" << clientId << ": 收到结果 - " << result.Operation 
                          << " " << result.Key << "=" << result.Value << std::endl;
                completedRequests++;
            } else {
                std::cout << "客户端" << clientId << ": 等待失败或超时" << std::endl;
            }
            
            waitApplyChChannel.erase(raftIndex); });
    }

    // 模拟Raft按顺序处理请求
    ioManager->scheduler([&waitApplyChChannel]()
                         {
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        
        for (int clientId = 1; clientId <= 3; ++clientId) {
            int raftIndex = 200 + clientId;
            
            if (waitApplyChChannel.find(raftIndex) != waitApplyChChannel.end()) {
                Op op;
                op.Operation = "Get";
                op.Key = "key_" + std::to_string(clientId);
                op.Value = "value_" + std::to_string(clientId);
                op.ClientId = "client_" + std::to_string(clientId);
                op.RequestId = clientId;
                
                auto result = waitApplyChChannel[raftIndex]->send(op);
                if (result == ChannelResult::SUCCESS) {
                    std::cout << "Raft: 处理完成raftIndex=" << raftIndex << std::endl;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(1500));
    ioManager->stop();

    std::cout << "完成的请求数: " << completedRequests.load() << "/3" << std::endl;
    std::cout << "多个等待Channel测试完成\n"
              << std::endl;
}

void testChannelTimeout()
{
    std::cout << "=== Channel超时测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "TimeoutTest");

    std::unordered_map<int, Channel<Op>::ptr> waitApplyChChannel;

    // 模拟超时场景
    ioManager->scheduler([&waitApplyChChannel]()
                         {
        int raftIndex = 300;
        std::cout << "客户端: 等待raftIndex=" << raftIndex << "（将会超时）" << std::endl;
        
        auto waitChannel = createChannel<Op>(1);
        waitApplyChChannel[raftIndex] = waitChannel;
        
        auto start = std::chrono::steady_clock::now();
        
        Op result;
        auto channelResult = waitChannel->receive(result, 500); // 500ms超时
        
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        if (channelResult == ChannelResult::TIMEOUT) {
            std::cout << "客户端: 正确超时，耗时=" << elapsed << "ms" << std::endl;
        } else {
            std::cout << "客户端: 意外结果，结果=" << (int)channelResult << std::endl;
        }
        
        waitApplyChChannel.erase(raftIndex); });

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "Channel超时测试完成\n"
              << std::endl;
}

void testChannelVsLockQueueComparison()
{
    std::cout << "=== Channel vs LockQueue 等待机制对比 ===" << std::endl;

    const int requestCount = 100;

    // 测试LockQueue方式
    auto start = std::chrono::high_resolution_clock::now();
    {
        std::unordered_map<int, LockQueue<Op> *> waitApplyCh;

        std::vector<std::thread> clients;
        std::vector<std::thread> servers;

        // 创建客户端线程
        for (int i = 0; i < requestCount; ++i)
        {
            clients.emplace_back([&waitApplyCh, i]()
                                 {
                auto lockQueue = new LockQueue<Op>();
                waitApplyCh[i] = lockQueue;
                
                Op result;
                bool success = lockQueue->timeOutPop(100, &result); // 100ms超时
                (void)success;
                
                delete lockQueue;
                waitApplyCh.erase(i); });
        }

        // 创建服务端线程
        for (int i = 0; i < requestCount; ++i)
        {
            servers.emplace_back([&waitApplyCh, i]()
                                 {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                
                if (waitApplyCh.find(i) != waitApplyCh.end()) {
                    Op op;
                    op.Operation = "Test";
                    op.Key = "key_" + std::to_string(i);
                    op.RequestId = i;
                    
                    waitApplyCh[i]->Push(op);
                } });
        }

        for (auto &t : clients)
            t.join();
        for (auto &t : servers)
            t.join();
    }
    auto lockQueueTime = std::chrono::duration_cast<std::chrono::microseconds>(
                             std::chrono::high_resolution_clock::now() - start)
                             .count();

    // 测试Channel方式
    start = std::chrono::high_resolution_clock::now();
    {
        auto ioManager = std::make_unique<IOManager>(4, false, "ComparisonTest");
        std::unordered_map<int, Channel<Op>::ptr> waitApplyChChannel;

        for (int i = 0; i < requestCount; ++i)
        {
            ioManager->scheduler([&waitApplyChChannel, i]()
                                 {
                auto channel = createChannel<Op>(1);
                waitApplyChChannel[i] = channel;
                
                Op result;
                auto channelResult = channel->receive(result, 100);
                (void)channelResult;
                
                waitApplyChChannel.erase(i); });

            ioManager->scheduler([&waitApplyChChannel, i]()
                                 {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                
                if (waitApplyChChannel.find(i) != waitApplyChChannel.end()) {
                    Op op;
                    op.Operation = "Test";
                    op.Key = "key_" + std::to_string(i);
                    op.RequestId = i;
                    
                    waitApplyChChannel[i]->send(op);
                } });
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        ioManager->stop();
    }
    auto channelTime = std::chrono::duration_cast<std::chrono::microseconds>(
                           std::chrono::high_resolution_clock::now() - start)
                           .count();

    std::cout << "LockQueue 处理 " << requestCount << " 个等待请求耗时: " << lockQueueTime << " 微秒" << std::endl;
    std::cout << "Channel 处理 " << requestCount << " 个等待请求耗时: " << channelTime << " 微秒" << std::endl;
    std::cout << "等待机制对比测试完成\n"
              << std::endl;
}

int main()
{
    std::cout << "waitApplyCh迁移测试" << std::endl;
    std::cout << "==================" << std::endl;

    try
    {
        testWaitApplyChChannel();
        testMultipleWaitChannels();
        testChannelTimeout();
        testChannelVsLockQueueComparison();

        std::cout << "所有测试完成！" << std::endl;
        std::cout << "\n✅ waitApplyCh Channel通信正常" << std::endl;
        std::cout << "✅ 多客户端等待机制正常" << std::endl;
        std::cout << "✅ 超时机制正常" << std::endl;
        std::cout << "✅ 性能对比测试完成" << std::endl;
        std::cout << "\n🎉 waitApplyCh成功从LockQueue迁移到Channel！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
