#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/fiber/migration_helper.h"
#include "raft-kv/raftCore/kvServer.h"
#include "raft-kv/raftCore/raft.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>

using namespace monsoon;

/**
 * @brief 完整的Channel集成测试
 * @details 测试整个Raft-KV系统中Channel的集成效果
 */

class ChannelIntegrationTester
{
public:
    struct TestResult
    {
        bool success = false;
        long durationMs = 0;
        int messagesProcessed = 0;
        double throughput = 0.0; // 消息/秒
        std::string errorMessage;
    };

    /**
     * @brief 测试ApplyMsg Channel的端到端性能
     */
    TestResult testApplyMsgChannelE2E()
    {
        std::cout << "=== ApplyMsg Channel端到端测试 ===" << std::endl;
        
        TestResult result;
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto ioManager = std::make_unique<IOManager>(4, false, "E2ETest");
            auto applyChan = createChannel<ApplyMsg>(100);
            
            const int messageCount = 1000;
            std::atomic<int> processedCount{0};
            
            // 模拟Raft发送ApplyMsg
            ioManager->scheduler([applyChan, messageCount]() {
                for (int i = 1; i <= messageCount; ++i) {
                    ApplyMsg msg;
                    msg.CommandValid = true;
                    msg.CommandIndex = i;
                    msg.Command = "PUT key" + std::to_string(i) + " value" + std::to_string(i);
                    
                    auto sendResult = applyChan->send(msg);
                    if (sendResult != ChannelResult::SUCCESS) {
                        std::cout << "发送失败: " << i << std::endl;
                        break;
                    }
                    
                    if (i % 100 == 0) {
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                }
                applyChan->close();
            });
            
            // 模拟KvServer处理ApplyMsg
            ioManager->scheduler([applyChan, &processedCount]() {
                while (true) {
                    ApplyMsg msg;
                    auto recvResult = applyChan->receive(msg, 1000);
                    
                    if (recvResult == ChannelResult::SUCCESS) {
                        // 模拟处理逻辑
                        processedCount++;
                        
                        if (processedCount % 200 == 0) {
                            std::cout << "已处理: " << processedCount << " 条消息" << std::endl;
                        }
                    } else if (recvResult == ChannelResult::CLOSED) {
                        std::cout << "Channel已关闭，处理完成" << std::endl;
                        break;
                    } else if (recvResult == ChannelResult::TIMEOUT) {
                        std::cout << "接收超时，可能处理完成" << std::endl;
                        break;
                    }
                }
            });
            
            std::this_thread::sleep_for(std::chrono::milliseconds(3000));
            ioManager->stop();
            
            auto end = std::chrono::high_resolution_clock::now();
            result.durationMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            result.messagesProcessed = processedCount.load();
            result.throughput = (double)result.messagesProcessed / (result.durationMs / 1000.0);
            result.success = (result.messagesProcessed >= messageCount * 0.95); // 95%成功率
            
            std::cout << "处理消息数: " << result.messagesProcessed << "/" << messageCount << std::endl;
            std::cout << "耗时: " << result.durationMs << "ms" << std::endl;
            std::cout << "吞吐量: " << result.throughput << " 消息/秒" << std::endl;
            
        } catch (const std::exception& e) {
            result.errorMessage = e.what();
        }
        
        std::cout << "ApplyMsg Channel端到端测试完成\n" << std::endl;
        return result;
    }

    /**
     * @brief 测试waitApplyCh Channel的并发性能
     */
    TestResult testWaitApplyChConcurrency()
    {
        std::cout << "=== waitApplyCh Channel并发测试 ===" << std::endl;
        
        TestResult result;
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto ioManager = std::make_unique<IOManager>(8, false, "ConcurrencyTest");
            std::unordered_map<int, Channel<Op>::ptr> waitApplyChChannel;
            
            const int clientCount = 50;
            const int requestsPerClient = 20;
            std::atomic<int> completedRequests{0};
            std::atomic<int> failedRequests{0};
            
            // 创建多个客户端协程
            for (int clientId = 1; clientId <= clientCount; ++clientId) {
                ioManager->scheduler([&waitApplyChChannel, &completedRequests, &failedRequests, 
                                    clientId, requestsPerClient]() {
                    for (int reqId = 1; reqId <= requestsPerClient; ++reqId) {
                        int raftIndex = clientId * 1000 + reqId;
                        
                        // 创建等待Channel
                        auto waitChannel = createChannel<Op>(1);
                        waitApplyChChannel[raftIndex] = waitChannel;
                        
                        // 等待结果
                        Op result;
                        auto channelResult = waitChannel->receive(result, 500); // 500ms超时
                        
                        if (channelResult == ChannelResult::SUCCESS) {
                            completedRequests++;
                        } else {
                            failedRequests++;
                        }
                        
                        // 清理
                        waitApplyChChannel.erase(raftIndex);
                        
                        std::this_thread::sleep_for(std::chrono::microseconds(100));
                    }
                });
            }
            
            // 模拟Raft处理请求
            ioManager->scheduler([&waitApplyChChannel, clientCount, requestsPerClient]() {
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                
                for (int clientId = 1; clientId <= clientCount; ++clientId) {
                    for (int reqId = 1; reqId <= requestsPerClient; ++reqId) {
                        int raftIndex = clientId * 1000 + reqId;
                        
                        if (waitApplyChChannel.find(raftIndex) != waitApplyChChannel.end()) {
                            Op op;
                            op.Operation = "Put";
                            op.Key = "key_" + std::to_string(clientId) + "_" + std::to_string(reqId);
                            op.Value = "value_" + std::to_string(clientId) + "_" + std::to_string(reqId);
                            op.ClientId = "client_" + std::to_string(clientId);
                            op.RequestId = reqId;
                            
                            waitApplyChChannel[raftIndex]->send(op);
                        }
                        
                        if ((clientId * requestsPerClient + reqId) % 100 == 0) {
                            std::this_thread::sleep_for(std::chrono::microseconds(50));
                        }
                    }
                }
            });
            
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            ioManager->stop();
            
            auto end = std::chrono::high_resolution_clock::now();
            result.durationMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            result.messagesProcessed = completedRequests.load();
            result.throughput = (double)result.messagesProcessed / (result.durationMs / 1000.0);
            
            int totalRequests = clientCount * requestsPerClient;
            result.success = (result.messagesProcessed >= totalRequests * 0.9); // 90%成功率
            
            std::cout << "总请求数: " << totalRequests << std::endl;
            std::cout << "成功请求数: " << completedRequests.load() << std::endl;
            std::cout << "失败请求数: " << failedRequests.load() << std::endl;
            std::cout << "成功率: " << (double)completedRequests.load() / totalRequests * 100 << "%" << std::endl;
            std::cout << "耗时: " << result.durationMs << "ms" << std::endl;
            std::cout << "吞吐量: " << result.throughput << " 请求/秒" << std::endl;
            
        } catch (const std::exception& e) {
            result.errorMessage = e.what();
        }
        
        std::cout << "waitApplyCh Channel并发测试完成\n" << std::endl;
        return result;
    }

    /**
     * @brief 测试ChannelSelector的性能
     */
    TestResult testChannelSelectorPerformance()
    {
        std::cout << "=== ChannelSelector性能测试 ===" << std::endl;
        
        TestResult result;
        auto start = std::chrono::high_resolution_clock::now();
        
        try {
            auto ioManager = std::make_unique<IOManager>(4, false, "SelectorPerfTest");
            
            auto ch1 = createChannel<int>(10);
            auto ch2 = createChannel<std::string>(10);
            auto ch3 = createChannel<Op>(10);
            
            const int selectCount = 100;
            std::atomic<int> selectSuccess{0};
            
            // 预先填充数据
            for (int i = 1; i <= 30; ++i) {
                ch1->trySend(i);
                ch2->trySend("data_" + std::to_string(i));
                
                Op op;
                op.Operation = "Test";
                op.Key = "key_" + std::to_string(i);
                op.RequestId = i;
                ch3->trySend(op);
            }
            
            // 执行多次select操作
            ioManager->scheduler([ch1, ch2, ch3, selectCount, &selectSuccess]() {
                for (int i = 0; i < selectCount; ++i) {
                    ChannelSelector selector;
                    
                    int intData;
                    std::string stringData;
                    Op opData;
                    
                    selector.addReceiveCase<int>(ch1, &intData);
                    selector.addReceiveCase<std::string>(ch2, &stringData);
                    selector.addReceiveCase<Op>(ch3, &opData);
                    
                    auto selectResult = selector.select(100); // 100ms超时
                    
                    if (selectResult.success) {
                        selectSuccess++;
                    }
                    
                    std::this_thread::sleep_for(std::chrono::microseconds(100));
                }
            });
            
            std::this_thread::sleep_for(std::chrono::milliseconds(1500));
            ioManager->stop();
            
            auto end = std::chrono::high_resolution_clock::now();
            result.durationMs = std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            result.messagesProcessed = selectSuccess.load();
            result.throughput = (double)result.messagesProcessed / (result.durationMs / 1000.0);
            result.success = (result.messagesProcessed >= selectCount * 0.8); // 80%成功率
            
            std::cout << "select操作数: " << selectCount << std::endl;
            std::cout << "成功select数: " << selectSuccess.load() << std::endl;
            std::cout << "成功率: " << (double)selectSuccess.load() / selectCount * 100 << "%" << std::endl;
            std::cout << "耗时: " << result.durationMs << "ms" << std::endl;
            std::cout << "吞吐量: " << result.throughput << " select/秒" << std::endl;
            
        } catch (const std::exception& e) {
            result.errorMessage = e.what();
        }
        
        std::cout << "ChannelSelector性能测试完成\n" << std::endl;
        return result;
    }

    /**
     * @brief 运行完整的集成测试套件
     */
    void runCompleteIntegrationTest()
    {
        std::cout << "🚀 开始完整的Channel集成测试" << std::endl;
        std::cout << "=================================" << std::endl;
        
        std::vector<TestResult> results;
        
        // 运行所有测试
        results.push_back(testApplyMsgChannelE2E());
        results.push_back(testWaitApplyChConcurrency());
        results.push_back(testChannelSelectorPerformance());
        
        // 汇总结果
        std::cout << "=== 测试结果汇总 ===" << std::endl;
        
        int passedTests = 0;
        double totalThroughput = 0.0;
        long totalDuration = 0;
        int totalMessages = 0;
        
        for (size_t i = 0; i < results.size(); ++i) {
            const auto& result = results[i];
            std::string testName;
            
            switch (i) {
                case 0: testName = "ApplyMsg E2E"; break;
                case 1: testName = "waitApplyCh并发"; break;
                case 2: testName = "ChannelSelector"; break;
            }
            
            std::cout << testName << ": " 
                      << (result.success ? "✅ 通过" : "❌ 失败") << std::endl;
            std::cout << "  - 处理消息: " << result.messagesProcessed << std::endl;
            std::cout << "  - 耗时: " << result.durationMs << "ms" << std::endl;
            std::cout << "  - 吞吐量: " << result.throughput << " ops/s" << std::endl;
            
            if (!result.errorMessage.empty()) {
                std::cout << "  - 错误: " << result.errorMessage << std::endl;
            }
            std::cout << std::endl;
            
            if (result.success) {
                passedTests++;
                totalThroughput += result.throughput;
                totalDuration += result.durationMs;
                totalMessages += result.messagesProcessed;
            }
        }
        
        std::cout << "=== 总体统计 ===" << std::endl;
        std::cout << "通过测试: " << passedTests << "/" << results.size() << std::endl;
        std::cout << "总处理消息: " << totalMessages << std::endl;
        std::cout << "总耗时: " << totalDuration << "ms" << std::endl;
        std::cout << "平均吞吐量: " << totalThroughput / results.size() << " ops/s" << std::endl;
        
        if (passedTests == results.size()) {
            std::cout << "\n🎉 所有测试通过！Channel集成成功！" << std::endl;
            std::cout << "✅ ApplyMsg Channel替换LockQueue成功" << std::endl;
            std::cout << "✅ waitApplyCh Channel并发性能良好" << std::endl;
            std::cout << "✅ ChannelSelector功能完整" << std::endl;
            std::cout << "✅ 整体系统性能稳定" << std::endl;
        } else {
            std::cout << "\n⚠️ 部分测试失败，需要进一步优化" << std::endl;
        }
    }
};

int main()
{
    try {
        ChannelIntegrationTester tester;
        tester.runCompleteIntegrationTest();
        
    } catch (const std::exception& e) {
        std::cerr << "集成测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
