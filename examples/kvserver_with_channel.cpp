/**
 * @file kvserver_with_channel.cpp
 * @brief 展示如何使用Channel改进KvServer的协程间通信
 * @details 这个示例展示了Channel相比LockQueue的优势
 */

#include "fiber/monsoon.h"
#include "common/util.h"
#include <iostream>
#include <unordered_map>
#include <memory>
#include <atomic>

using namespace monsoon;

// 模拟ApplyMsg结构
struct ApplyMsg
{
    bool CommandValid = false;
    std::string Command;
    int CommandIndex = 0;
    bool SnapshotValid = false;
    std::string Snapshot;
    int SnapshotTerm = 0;
    int SnapshotIndex = 0;
};

// 模拟Op结构
struct Op
{
    std::string Operation; // "Get" "Put" "Append"
    std::string Key;
    std::string Value;
    std::string ClientId;
    int RequestId;
};

/**
 * @brief 使用Channel改进的KvServer示例
 * @details 展示Channel如何简化协程间通信，提供更好的编程体验
 */
class KvServerWithChannel
{
public:
    KvServerWithChannel(int serverId) : serverId_(serverId)
    {
        // 初始化IOManager
        ioManager_ = std::make_unique<IOManager>(3, true, "KvServer");
        
        // 创建Channel替代LockQueue
        applyChannel_ = createChannel<ApplyMsg>(100);
        
        // 为每个等待的操作创建Channel
        // 这比使用map<int, LockQueue*>更安全和清晰
    }
    
    void start()
    {
        ioManager_->start();
        
        // 启动apply协程，处理来自Raft的消息
        ioManager_->scheduler([this]() {
            this->applier();
        });
        
        std::cout << "KvServer " << serverId_ << " 启动完成" << std::endl;
    }
    
    void stop()
    {
        applyChannel_->close();
        ioManager_->stop();
        std::cout << "KvServer " << serverId_ << " 停止完成" << std::endl;
    }
    
    /**
     * @brief 处理Get请求（使用Channel）
     */
    std::string Get(const std::string& key, const std::string& clientId, int requestId)
    {
        Op op;
        op.Operation = "Get";
        op.Key = key;
        op.ClientId = clientId;
        op.RequestId = requestId;
        
        // 模拟提交给Raft
        int raftIndex = submitToRaft(op);
        
        // 创建等待Channel
        auto waitChannel = createChannel<Op>(1);
        
        {
            std::lock_guard<std::mutex> lock(waitChannelsMutex_);
            waitChannels_[raftIndex] = waitChannel;
        }
        
        // 等待Raft提交结果
        Op result;
        auto status = waitChannel->receive(result, 5000); // 5秒超时
        
        // 清理等待Channel
        {
            std::lock_guard<std::mutex> lock(waitChannelsMutex_);
            waitChannels_.erase(raftIndex);
        }
        
        if (status == ChannelResult::SUCCESS) {
            // 从状态机获取值
            std::lock_guard<std::mutex> lock(stateMutex_);
            auto it = kvStore_.find(key);
            return (it != kvStore_.end()) ? it->second : "";
        } else {
            std::cout << "Get操作超时或失败" << std::endl;
            return "";
        }
    }
    
    /**
     * @brief 处理Put请求（使用Channel）
     */
    bool Put(const std::string& key, const std::string& value, 
             const std::string& clientId, int requestId)
    {
        Op op;
        op.Operation = "Put";
        op.Key = key;
        op.Value = value;
        op.ClientId = clientId;
        op.RequestId = requestId;
        
        return executeOperation(op);
    }
    
    /**
     * @brief 处理Append请求（使用Channel）
     */
    bool Append(const std::string& key, const std::string& value,
                const std::string& clientId, int requestId)
    {
        Op op;
        op.Operation = "Append";
        op.Key = key;
        op.Value = value;
        op.ClientId = clientId;
        op.RequestId = requestId;
        
        return executeOperation(op);
    }
    
    /**
     * @brief 模拟从Raft接收ApplyMsg
     */
    void receiveFromRaft(const ApplyMsg& msg)
    {
        // 使用Channel发送消息，比LockQueue更安全
        auto result = applyChannel_->trySend(msg);
        if (result != ChannelResult::SUCCESS) {
            std::cout << "警告: 无法发送ApplyMsg到Channel" << std::endl;
        }
    }

private:
    /**
     * @brief 执行操作的通用方法
     */
    bool executeOperation(const Op& op)
    {
        int raftIndex = submitToRaft(op);
        
        auto waitChannel = createChannel<Op>(1);
        
        {
            std::lock_guard<std::mutex> lock(waitChannelsMutex_);
            waitChannels_[raftIndex] = waitChannel;
        }
        
        Op result;
        auto status = waitChannel->receive(result, 5000);
        
        {
            std::lock_guard<std::mutex> lock(waitChannelsMutex_);
            waitChannels_.erase(raftIndex);
        }
        
        return status == ChannelResult::SUCCESS;
    }
    
    /**
     * @brief 模拟提交操作到Raft
     */
    int submitToRaft(const Op& op)
    {
        static std::atomic<int> indexCounter{1};
        int index = indexCounter++;
        
        std::cout << "提交操作到Raft: " << op.Operation 
                  << " " << op.Key << " -> index " << index << std::endl;
        
        // 模拟异步提交到Raft，稍后会通过receiveFromRaft返回结果
        ioManager_->scheduler([this, op, index]() {
            // 模拟Raft处理延迟
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            ApplyMsg msg;
            msg.CommandValid = true;
            msg.CommandIndex = index;
            msg.Command = op.Operation + "|" + op.Key + "|" + op.Value;
            
            this->receiveFromRaft(msg);
        });
        
        return index;
    }
    
    /**
     * @brief Apply协程，处理来自Raft的消息
     * @details 这个协程替代了原来的applier线程，使用Channel接收消息
     */
    void applier()
    {
        std::cout << "Apply协程启动" << std::endl;
        
        while (true) {
            ApplyMsg msg;
            auto result = applyChannel_->receive(msg, 1000); // 1秒超时
            
            if (result == ChannelResult::SUCCESS) {
                if (msg.CommandValid) {
                    applyCommand(msg);
                } else if (msg.SnapshotValid) {
                    applySnapshot(msg);
                }
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "Apply Channel已关闭，退出applier" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                // 超时是正常的，继续循环
                continue;
            }
        }
    }
    
    /**
     * @brief 应用命令到状态机
     */
    void applyCommand(const ApplyMsg& msg)
    {
        std::cout << "应用命令: index=" << msg.CommandIndex 
                  << ", command=" << msg.Command << std::endl;
        
        // 解析命令
        Op op = parseCommand(msg.Command);
        
        // 应用到状态机
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            if (op.Operation == "Put") {
                kvStore_[op.Key] = op.Value;
            } else if (op.Operation == "Append") {
                kvStore_[op.Key] += op.Value;
            }
            // Get操作不修改状态
        }
        
        // 通知等待的协程
        notifyWaiter(msg.CommandIndex, op);
    }
    
    /**
     * @brief 应用快照
     */
    void applySnapshot(const ApplyMsg& msg)
    {
        std::cout << "应用快照: term=" << msg.SnapshotTerm 
                  << ", index=" << msg.SnapshotIndex << std::endl;
        // 快照应用逻辑...
    }
    
    /**
     * @brief 解析命令字符串
     */
    Op parseCommand(const std::string& command)
    {
        Op op;
        // 简单的解析逻辑
        size_t pos1 = command.find('|');
        size_t pos2 = command.find('|', pos1 + 1);
        
        if (pos1 != std::string::npos) {
            op.Operation = command.substr(0, pos1);
            if (pos2 != std::string::npos) {
                op.Key = command.substr(pos1 + 1, pos2 - pos1 - 1);
                op.Value = command.substr(pos2 + 1);
            } else {
                op.Key = command.substr(pos1 + 1);
            }
        }
        
        return op;
    }
    
    /**
     * @brief 通知等待的协程
     */
    void notifyWaiter(int index, const Op& op)
    {
        std::lock_guard<std::mutex> lock(waitChannelsMutex_);
        auto it = waitChannels_.find(index);
        if (it != waitChannels_.end()) {
            // 使用Channel发送结果，比LockQueue更安全
            auto result = it->second->trySend(op);
            if (result != ChannelResult::SUCCESS) {
                std::cout << "警告: 无法通知等待的协程" << std::endl;
            }
        }
    }

private:
    int serverId_;
    std::unique_ptr<IOManager> ioManager_;
    
    // 使用Channel替代LockQueue
    Channel<ApplyMsg>::ptr applyChannel_;
    
    // 等待Channel映射，替代原来的map<int, LockQueue<Op>*>
    std::unordered_map<int, Channel<Op>::ptr> waitChannels_;
    std::mutex waitChannelsMutex_;
    
    // KV存储状态机
    std::unordered_map<std::string, std::string> kvStore_;
    std::mutex stateMutex_;
};

// 演示函数
void demonstrateKvServerWithChannel()
{
    std::cout << "=== KvServer with Channel 演示 ===" << std::endl;
    
    KvServerWithChannel server(1);
    server.start();
    
    // 模拟客户端请求
    std::thread client1([&server]() {
        std::cout << "\n客户端1开始操作..." << std::endl;
        
        // Put操作
        bool success = server.Put("key1", "value1", "client1", 1);
        std::cout << "Put key1=value1: " << (success ? "成功" : "失败") << std::endl;
        
        // Get操作
        std::string value = server.Get("key1", "client1", 2);
        std::cout << "Get key1: " << value << std::endl;
        
        // Append操作
        success = server.Append("key1", "_appended", "client1", 3);
        std::cout << "Append key1: " << (success ? "成功" : "失败") << std::endl;
        
        // 再次Get
        value = server.Get("key1", "client1", 4);
        std::cout << "Get key1 after append: " << value << std::endl;
    });
    
    std::thread client2([&server]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        std::cout << "\n客户端2开始操作..." << std::endl;
        
        bool success = server.Put("key2", "value2", "client2", 1);
        std::cout << "Put key2=value2: " << (success ? "成功" : "失败") << std::endl;
        
        std::string value = server.Get("key2", "client2", 2);
        std::cout << "Get key2: " << value << std::endl;
    });
    
    client1.join();
    client2.join();
    
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    server.stop();
    
    std::cout << "\nKvServer with Channel 演示完成" << std::endl;
    std::cout << "\nChannel相比LockQueue的优势:" << std::endl;
    std::cout << "1. 类型安全 - 编译时检查数据类型" << std::endl;
    std::cout << "2. 自动协程调度 - 无需手动管理协程状态" << std::endl;
    std::cout name="3. 超时支持 - 内置超时机制，避免死锁" << std::endl;
    std::cout << "4. 资源管理 - 自动清理，无内存泄漏风险" << std::endl;
    std::cout << "5. 更清晰的代码 - 减少锁的使用，提高可读性" << std::endl;
}

int main()
{
    try {
        demonstrateKvServerWithChannel();
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
