#include "raft-kv/fiber/final_channel.h"
#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

using namespace monsoon;

/**
 * @brief 最终Channel系统演示
 */

void demoBasicChannelOperations()
{
    std::cout << "=== 基本Channel操作演示 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(2, false, "BasicDemo");
    
    // 创建有缓冲的Channel
    auto ch = createFinalChannel<int>(3);
    
    // 发送方协程
    ioManager->scheduler([ch]() {
        std::cout << "发送方: 开始发送数据" << std::endl;
        
        for (int i = 1; i <= 5; ++i) {
            auto result = ch->send(i * 10);
            std::cout << "发送 " << (i * 10) << ": " 
                      << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        ch->close();
        std::cout << "发送方: 关闭Channel" << std::endl;
    });
    
    // 接收方协程
    ioManager->scheduler([ch]() {
        std::cout << "接收方: 开始接收数据" << std::endl;
        
        while (true) {
            int data;
            auto result = ch->receive(data, 1000);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收到: " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "接收方: Channel已关闭" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "接收方: 接收超时" << std::endl;
                break;
            }
        }
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "基本Channel操作演示完成\n" << std::endl;
}

void demoChannelSelector()
{
    std::cout << "=== ChannelSelector演示 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(3, false, "SelectorDemo");
    
    auto ch1 = createFinalChannel<int>(2);
    auto ch2 = createFinalChannel<std::string>(2);
    auto ch3 = createFinalChannel<Op>(2);
    
    // 预先发送一些数据
    ch1->trySend(42);
    ch2->trySend("Hello");
    
    Op op;
    op.Operation = "PUT";
    op.Key = "demo_key";
    op.Value = "demo_value";
    ch3->trySend(op);
    
    // 使用ChannelSelector
    ioManager->scheduler([ch1, ch2, ch3]() {
        std::cout << "开始ChannelSelector演示" << std::endl;
        
        for (int round = 1; round <= 3; ++round) {
            std::cout << "第" << round << "轮选择:" << std::endl;
            
            FinalChannelSelector selector;
            
            int intData;
            std::string stringData;
            Op opData;
            
            selector.addReceiveCase<int>(ch1, &intData, [](const int& data, bool success) {
                std::cout << "  从ch1接收到int: " << data << std::endl;
            });
            
            selector.addReceiveCase<std::string>(ch2, &stringData, [](const std::string& data, bool success) {
                std::cout << "  从ch2接收到string: " << data << std::endl;
            });
            
            selector.addReceiveCase<Op>(ch3, &opData, [](const Op& data, bool success) {
                std::cout << "  从ch3接收到Op: " << data.Operation << " " << data.Key << std::endl;
            });
            
            selector.addDefaultCase([]() {
                std::cout << "  执行默认case" << std::endl;
            });
            
            auto result = selector.select(100);
            std::cout << "  选择结果: case=" << result.caseIndex 
                      << ", 成功=" << result.success << std::endl;
        }
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ioManager->stop();
    std::cout << "ChannelSelector演示完成\n" << std::endl;
}

void demoRaftKVIntegration()
{
    std::cout << "=== Raft-KV集成演示 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(4, false, "RaftKVDemo");
    
    // 模拟applyChan
    auto applyChan = createFinalChannel<ApplyMsg>(10);
    
    // 模拟waitApplyCh
    std::unordered_map<int, typename FinalChannel<Op>::ptr> waitApplyChChannel;
    
    // 模拟Raft发送ApplyMsg
    ioManager->scheduler([applyChan]() {
        std::cout << "Raft: 开始发送ApplyMsg" << std::endl;
        
        for (int i = 1; i <= 3; ++i) {
            ApplyMsg msg;
            msg.CommandValid = true;
            msg.CommandIndex = i;
            msg.Command = "PUT key" + std::to_string(i) + " value" + std::to_string(i);
            
            applyChan->send(msg);
            std::cout << "Raft: 发送ApplyMsg " << i << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });
    
    // 模拟KvServer处理ApplyMsg
    ioManager->scheduler([applyChan, &waitApplyChChannel]() {
        std::cout << "KvServer: 开始处理ApplyMsg" << std::endl;
        
        for (int i = 1; i <= 3; ++i) {
            ApplyMsg msg;
            auto result = applyChan->receive(msg, 1000);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "KvServer: 处理ApplyMsg " << msg.CommandIndex 
                          << " - " << msg.Command << std::endl;
                
                // 模拟通知等待的客户端
                if (waitApplyChChannel.find(msg.CommandIndex) != waitApplyChChannel.end()) {
                    Op op;
                    op.Operation = "PUT";
                    op.Key = "key" + std::to_string(msg.CommandIndex);
                    op.Value = "value" + std::to_string(msg.CommandIndex);
                    
                    waitApplyChChannel[msg.CommandIndex]->send(op);
                }
            }
        }
    });
    
    // 模拟客户端等待
    ioManager->scheduler([&waitApplyChChannel]() {
        std::cout << "Client: 等待操作完成" << std::endl;
        
        int raftIndex = 2;
        auto waitChannel = createFinalChannel<Op>(1);
        waitApplyChChannel[raftIndex] = waitChannel;
        
        Op result;
        auto channelResult = waitChannel->receive(result, 1000);
        
        if (channelResult == ChannelResult::SUCCESS) {
            std::cout << "Client: 操作完成 - " << result.Operation 
                      << " " << result.Key << "=" << result.Value << std::endl;
        } else {
            std::cout << "Client: 等待超时或失败" << std::endl;
        }
        
        waitApplyChChannel.erase(raftIndex);
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(800));
    ioManager->stop();
    std::cout << "Raft-KV集成演示完成\n" << std::endl;
}

void demoPerformanceComparison()
{
    std::cout << "=== 性能对比演示 ===" << std::endl;
    
    const int messageCount = 1000;
    
    // 测试LockQueue
    auto start = std::chrono::high_resolution_clock::now();
    {
        LockQueue<int> lockQueue;
        
        std::thread producer([&lockQueue, messageCount]() {
            for (int i = 0; i < messageCount; ++i) {
                lockQueue.Push(i);
            }
        });
        
        std::thread consumer([&lockQueue, messageCount]() {
            for (int i = 0; i < messageCount; ++i) {
                int data = lockQueue.Pop();
                (void)data;
            }
        });
        
        producer.join();
        consumer.join();
    }
    auto lockQueueTime = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::high_resolution_clock::now() - start).count();
    
    // 测试FinalChannel
    start = std::chrono::high_resolution_clock::now();
    {
        auto ioManager = std::make_unique<IOManager>(2, false, "PerfTest");
        auto channel = createFinalChannel<int>(100);
        
        ioManager->scheduler([channel, messageCount]() {
            for (int i = 0; i < messageCount; ++i) {
                channel->send(i);
            }
        });
        
        ioManager->scheduler([channel, messageCount]() {
            for (int i = 0; i < messageCount; ++i) {
                int data;
                channel->receive(data);
            }
        });
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        ioManager->stop();
    }
    auto channelTime = std::chrono::duration_cast<std::chrono::microseconds>(
        std::chrono::high_resolution_clock::now() - start).count();
    
    std::cout << "处理 " << messageCount << " 个消息:" << std::endl;
    std::cout << "LockQueue耗时: " << lockQueueTime << " 微秒" << std::endl;
    std::cout << "FinalChannel耗时: " << channelTime << " 微秒" << std::endl;
    
    if (channelTime < lockQueueTime) {
        std::cout << "FinalChannel性能提升: " 
                  << ((double)(lockQueueTime - channelTime) / lockQueueTime * 100) << "%" << std::endl;
    } else {
        std::cout << "LockQueue性能更好: " 
                  << ((double)(channelTime - lockQueueTime) / channelTime * 100) << "%" << std::endl;
    }
    
    std::cout << "性能对比演示完成\n" << std::endl;
}

int main()
{
    std::cout << "🚀 最终Channel系统演示" << std::endl;
    std::cout << "======================" << std::endl;
    
    try {
        demoBasicChannelOperations();
        demoChannelSelector();
        demoRaftKVIntegration();
        demoPerformanceComparison();
        
        std::cout << "🎉 所有演示完成！" << std::endl;
        std::cout << "\n✅ 基本Channel操作正常" << std::endl;
        std::cout << "✅ ChannelSelector功能完整" << std::endl;
        std::cout << "✅ Raft-KV集成成功" << std::endl;
        std::cout << "✅ 性能对比完成" << std::endl;
        std::cout << "\n🎯 FinalChannel系统已准备就绪！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
