# Channel示例和测试的CMakeLists.txt

cmake_minimum_required(VERSION 3.10)

# 设置项目名称
project(ChannelExamples)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")

# 包含头文件目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/include/raft-kv)

# 查找依赖库
find_package(Threads REQUIRED)
find_package(Boost REQUIRED COMPONENTS serialization)

# 设置源文件目录
set(FIBER_SRC_DIR ${CMAKE_SOURCE_DIR}/src/fiber)
set(COMMON_SRC_DIR ${CMAKE_SOURCE_DIR}/src/common)

# 收集fiber库源文件
file(GLOB FIBER_SOURCES 
    ${FIBER_SRC_DIR}/*.cpp
)

# 收集common库源文件（如果需要）
file(GLOB COMMON_SOURCES 
    ${COMMON_SRC_DIR}/*.cpp
)

# 创建fiber库
add_library(fiber_lib STATIC ${FIBER_SOURCES})
target_link_libraries(fiber_lib Threads::Threads)

# 如果有common源文件，创建common库
if(COMMON_SOURCES)
    add_library(common_lib STATIC ${COMMON_SOURCES})
    target_link_libraries(common_lib Boost::serialization)
endif()

# Channel测试可执行文件
add_executable(channel_test 
    ${CMAKE_SOURCE_DIR}/tests/channel_test.cpp
)
target_link_libraries(channel_test 
    fiber_lib 
    Threads::Threads
)

# Channel示例可执行文件
add_executable(channel_example 
    channel_example.cpp
)
target_link_libraries(channel_example 
    fiber_lib 
    Threads::Threads
)

# KvServer with Channel示例可执行文件
add_executable(kvserver_with_channel 
    kvserver_with_channel.cpp
)
target_link_libraries(kvserver_with_channel 
    fiber_lib 
    Threads::Threads
    Boost::serialization
)

# 如果有common库，链接它
if(COMMON_SOURCES)
    target_link_libraries(channel_example common_lib)
    target_link_libraries(kvserver_with_channel common_lib)
endif()

# 设置输出目录
set_target_properties(channel_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
set_target_properties(channel_example PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
set_target_properties(kvserver_with_channel PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 添加自定义目标来运行测试
add_custom_target(run_channel_test
    COMMAND ${CMAKE_BINARY_DIR}/bin/channel_test
    DEPENDS channel_test
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running Channel tests"
)

add_custom_target(run_channel_example
    COMMAND ${CMAKE_BINARY_DIR}/bin/channel_example
    DEPENDS channel_example
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running Channel example"
)

add_custom_target(run_kvserver_example
    COMMAND ${CMAKE_BINARY_DIR}/bin/kvserver_with_channel
    DEPENDS kvserver_with_channel
    WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
    COMMENT "Running KvServer with Channel example"
)

# 添加一个运行所有示例的目标
add_custom_target(run_all_examples
    DEPENDS run_channel_test run_channel_example run_kvserver_example
    COMMENT "Running all Channel examples and tests"
)

# 打印构建信息
message(STATUS "Channel Examples Configuration:")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  Compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "  Fiber Sources: ${FIBER_SOURCES}")
if(COMMON_SOURCES)
    message(STATUS "  Common Sources: ${COMMON_SOURCES}")
endif()
