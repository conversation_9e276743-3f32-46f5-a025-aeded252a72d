#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/raftCore/ApplyMsg.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>

using namespace monsoon;

/**
 * @brief 测试applyChan从LockQueue迁移到Channel的功能
 * @details 模拟Raft和KvServer之间的通信
 */

void testApplyMsgChannel()
{
    std::cout << "=== ApplyMsg Channel测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, false, "ApplyMsgTest");

    // 创建ApplyMsg Channel，模拟applyChan
    auto applyChan = createChannel<ApplyMsg>(10);

    // 模拟Raft节点发送ApplyMsg
    ioManager->scheduler([applyChan]()
                         {
        std::cout << "Raft模拟器: 开始发送ApplyMsg" << std::endl;
        
        for (int i = 1; i <= 5; ++i) {
            ApplyMsg msg;
            msg.CommandValid = true;
            msg.CommandIndex = i;
            msg.Command = "SET key" + std::to_string(i) + " value" + std::to_string(i);
            
            auto result = applyChan->send(msg);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "Raft: 发送ApplyMsg成功，索引=" << i << std::endl;
            } else {
                std::cout << "Raft: 发送ApplyMsg失败，索引=" << i << std::endl;
            }
            
            // 模拟发送间隔
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 发送快照消息
        ApplyMsg snapshotMsg;
        snapshotMsg.SnapshotValid = true;
        snapshotMsg.Snapshot = "snapshot_data";
        snapshotMsg.SnapshotTerm = 1;
        snapshotMsg.SnapshotIndex = 5;
        
        auto result = applyChan->send(snapshotMsg);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "Raft: 发送快照消息成功" << std::endl;
        }
        
        applyChan->close();
        std::cout << "Raft模拟器: 完成发送，关闭Channel" << std::endl; });

    // 模拟KvServer接收ApplyMsg
    ioManager->scheduler([applyChan]()
                         {
        std::cout << "KvServer模拟器: 开始接收ApplyMsg" << std::endl;
        
        while (true) {
            ApplyMsg msg;
            auto result = applyChan->receive(msg, 2000); // 2秒超时
            
            if (result == ChannelResult::SUCCESS) {
                if (msg.CommandValid) {
                    std::cout << "KvServer: 接收到命令，索引=" << msg.CommandIndex 
                              << ", 命令=" << msg.Command << std::endl;
                } else if (msg.SnapshotValid) {
                    std::cout << "KvServer: 接收到快照，任期=" << msg.SnapshotTerm 
                              << ", 索引=" << msg.SnapshotIndex << std::endl;
                }
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "KvServer: applyChan已关闭，退出接收循环" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "KvServer: 接收超时" << std::endl;
                break;
            } else {
                std::cout << "KvServer: 接收失败，结果=" << (int)result << std::endl;
                break;
            }
        }
        
        std::cout << "KvServer模拟器: 完成接收" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    ioManager->stop();
    std::cout << "ApplyMsg Channel测试完成\n"
              << std::endl;
}

void testChannelVsLockQueuePerformance()
{
    std::cout << "=== Channel vs LockQueue 性能对比 ===" << std::endl;

    const int messageCount = 1000;

    // 测试LockQueue性能
    auto start = std::chrono::high_resolution_clock::now();
    {
        LockQueue<ApplyMsg> lockQueue;

        std::thread producer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                ApplyMsg msg;
                msg.CommandValid = true;
                msg.CommandIndex = i;
                msg.Command = "test_command_" + std::to_string(i);
                lockQueue.Push(msg);
            } });

        std::thread consumer([&lockQueue, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                ApplyMsg msg = lockQueue.Pop();
                (void)msg; // 避免未使用变量警告
            } });

        producer.join();
        consumer.join();
    }
    auto lockQueueTime = std::chrono::duration_cast<std::chrono::microseconds>(
                             std::chrono::high_resolution_clock::now() - start)
                             .count();

    // 测试Channel性能
    start = std::chrono::high_resolution_clock::now();
    {
        auto ioManager = std::make_unique<IOManager>(2, false, "PerfTest");
        auto channel = createChannel<ApplyMsg>(100);

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                ApplyMsg msg;
                msg.CommandValid = true;
                msg.CommandIndex = i;
                msg.Command = "test_command_" + std::to_string(i);
                channel->send(msg);
            } });

        ioManager->scheduler([channel, messageCount]()
                             {
            for (int i = 0; i < messageCount; ++i) {
                ApplyMsg msg;
                channel->receive(msg);
            } });

        std::this_thread::sleep_for(std::chrono::milliseconds(200));
        ioManager->stop();
    }
    auto channelTime = std::chrono::duration_cast<std::chrono::microseconds>(
                           std::chrono::high_resolution_clock::now() - start)
                           .count();

    std::cout << "LockQueue 处理 " << messageCount << " 个ApplyMsg耗时: " << lockQueueTime << " 微秒" << std::endl;
    std::cout << "Channel 处理 " << messageCount << " 个ApplyMsg耗时: " << channelTime << " 微秒" << std::endl;

    if (channelTime < lockQueueTime)
    {
        std::cout << "Channel性能提升: " << ((double)(lockQueueTime - channelTime) / lockQueueTime * 100) << "%" << std::endl;
    }
    else
    {
        std::cout << "LockQueue性能更好: " << ((double)(channelTime - lockQueueTime) / channelTime * 100) << "%" << std::endl;
    }

    std::cout << "性能对比测试完成\n"
              << std::endl;
}

void testChannelReliability()
{
    std::cout << "=== Channel可靠性测试 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(3, false, "ReliabilityTest");
    auto channel = createChannel<ApplyMsg>(5);

    std::atomic<int> sentCount{0};
    std::atomic<int> receivedCount{0};

    // 多个发送者
    for (int producerId = 0; producerId < 3; ++producerId)
    {
        ioManager->scheduler([channel, producerId, &sentCount]()
                             {
            for (int i = 0; i < 10; ++i) {
                ApplyMsg msg;
                msg.CommandValid = true;
                msg.CommandIndex = producerId * 100 + i;
                msg.Command = "producer_" + std::to_string(producerId) + "_msg_" + std::to_string(i);
                
                auto result = channel->send(msg, 1000); // 1秒超时
                if (result == ChannelResult::SUCCESS) {
                    sentCount++;
                }
                
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            } });
    }

    // 单个接收者
    ioManager->scheduler([channel, &receivedCount]()
                         {
        while (receivedCount.load() < 30) { // 期望接收30个消息
            ApplyMsg msg;
            auto result = channel->receive(msg, 2000); // 2秒超时
            
            if (result == ChannelResult::SUCCESS) {
                receivedCount++;
                std::cout << "接收到消息: " << msg.Command << std::endl;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "接收超时，当前已接收: " << receivedCount.load() << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(3000));
    ioManager->stop();

    std::cout << "发送消息数: " << sentCount.load() << std::endl;
    std::cout << "接收消息数: " << receivedCount.load() << std::endl;
    std::cout << "消息丢失率: " << ((double)(sentCount.load() - receivedCount.load()) / sentCount.load() * 100) << "%" << std::endl;
    std::cout << "Channel可靠性测试完成\n"
              << std::endl;
}

int main()
{
    std::cout << "applyChan迁移测试" << std::endl;
    std::cout << "=================" << std::endl;

    try
    {
        testApplyMsgChannel();
        testChannelVsLockQueuePerformance();
        testChannelReliability();

        std::cout << "所有测试完成！" << std::endl;
        std::cout << "\n✅ ApplyMsg Channel通信正常" << std::endl;
        std::cout << "✅ 性能对比测试完成" << std::endl;
        std::cout << "✅ 可靠性测试通过" << std::endl;
        std::cout << "\n🎉 applyChan成功从LockQueue迁移到Channel！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
