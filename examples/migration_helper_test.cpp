#include "raft-kv/fiber/migration_helper.h"
#include "raft-kv/fiber/monsoon.h"
#include "raft-kv/common/util.h"
#include <iostream>
#include <string>

using namespace monsoon;

/**
 * @brief 测试迁移助手工具
 */

void testLockQueueWrapper()
{
    std::cout << "=== LockQueue包装器测试 ===" << std::endl;
    
    auto lockQueue = std::make_shared<LockQueue<int>>();
    auto wrapper = MigrationHelper::wrapLockQueue(lockQueue);
    
    // 测试发送
    auto result = wrapper->send(42);
    std::cout << "发送结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    result = wrapper->send(100);
    std::cout << "发送结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    
    // 测试接收
    int data;
    result = wrapper->receive(data);
    std::cout << "接收结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") 
              << ", 数据: " << data << std::endl;
    
    result = wrapper->receive(data);
    std::cout << "接收结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") 
              << ", 数据: " << data << std::endl;
    
    // 测试超时接收
    result = wrapper->receive(data, 100); // 100ms超时
    std::cout << "超时接收结果: " << (result == ChannelResult::TIMEOUT ? "超时" : "成功") << std::endl;
    
    std::cout << "LockQueue包装器测试完成\n" << std::endl;
}

void testMigrationFromLockQueue()
{
    std::cout << "=== LockQueue迁移测试 ===" << std::endl;
    
    // 创建并填充LockQueue
    auto lockQueue = std::make_shared<LockQueue<std::string>>();
    lockQueue->Push("data1");
    lockQueue->Push("data2");
    lockQueue->Push("data3");
    
    std::cout << "原LockQueue中有3个数据项" << std::endl;
    
    // 迁移到Channel
    auto channel = MigrationHelper::migrateFromLockQueue<std::string>(lockQueue, 5);
    
    std::cout << "迁移完成，测试Channel中的数据:" << std::endl;
    
    // 从Channel读取数据
    for (int i = 0; i < 5; ++i) {
        std::string data;
        auto result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "从Channel读取: " << data << std::endl;
        } else {
            std::cout << "Channel中没有更多数据" << std::endl;
            break;
        }
    }
    
    std::cout << "LockQueue迁移测试完成\n" << std::endl;
}

void testBenchmark()
{
    std::cout << "=== 性能基准测试 ===" << std::endl;
    
    const int messageCount = 1000;
    
    auto result = MigrationHelper::benchmark<int>(messageCount, 10);
    
    std::cout << "消息数量: " << messageCount << std::endl;
    std::cout << "LockQueue耗时: " << result.lockQueueTimeUs << " 微秒" << std::endl;
    std::cout << "Channel耗时: " << result.channelTimeUs << " 微秒" << std::endl;
    
    if (result.improvement > 0) {
        std::cout << "Channel性能提升: " << result.improvement << "%" << std::endl;
    } else {
        std::cout << "LockQueue性能更好: " << (-result.improvement) << "%" << std::endl;
    }
    
    std::cout << "性能基准测试完成\n" << std::endl;
}

void testMigrationConfig()
{
    std::cout << "=== 迁移配置测试 ===" << std::endl;
    
    // 测试默认配置
    MigrationConfig config;
    config.printConfig();
    
    // 测试配置修改
    config.enableChannel = false;
    config.enableLockQueue = true;
    config.defaultChannelCapacity = 20;
    config.defaultTimeoutMs = 10000;
    
    std::cout << "\n修改后的配置:" << std::endl;
    config.printConfig();
    
    // 测试环境变量加载
    std::cout << "\n测试环境变量加载:" << std::endl;
    setenv("RAFT_KV_USE_CHANNEL", "false", 1);
    setenv("RAFT_KV_CHANNEL_CAPACITY", "50", 1);
    setenv("RAFT_KV_TIMEOUT_MS", "3000", 1);
    
    config.loadFromEnvironment();
    config.printConfig();
    
    // 清理环境变量
    unsetenv("RAFT_KV_USE_CHANNEL");
    unsetenv("RAFT_KV_CHANNEL_CAPACITY");
    unsetenv("RAFT_KV_TIMEOUT_MS");
    
    std::cout << "迁移配置测试完成\n" << std::endl;
}

void testCompatibilityLayer()
{
    std::cout << "=== 兼容层测试 ===" << std::endl;
    
    auto ioManager = std::make_unique<IOManager>(2, false, "CompatibilityTest");
    
    // 创建LockQueue和Channel，测试它们的互操作性
    auto lockQueue = std::make_shared<LockQueue<Op>>();
    auto channel = createChannel<Op>(5);
    auto wrapper = MigrationHelper::wrapLockQueue(lockQueue);
    
    // 测试数据
    Op testOp;
    testOp.Operation = "Test";
    testOp.Key = "compatibility_key";
    testOp.Value = "compatibility_value";
    testOp.ClientId = "test_client";
    testOp.RequestId = 1;
    
    // 向LockQueue包装器发送数据
    ioManager->scheduler([wrapper, testOp]() {
        std::cout << "向LockQueue包装器发送数据" << std::endl;
        auto result = wrapper->send(testOp);
        std::cout << "发送结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    });
    
    // 从LockQueue包装器接收数据
    ioManager->scheduler([wrapper]() {
        std::cout << "从LockQueue包装器接收数据" << std::endl;
        Op receivedOp;
        auto result = wrapper->receive(receivedOp, 1000);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "接收成功: " << receivedOp.Operation << " " 
                      << receivedOp.Key << "=" << receivedOp.Value << std::endl;
        } else {
            std::cout << "接收失败或超时" << std::endl;
        }
    });
    
    // 向真正的Channel发送数据
    ioManager->scheduler([channel, testOp]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        std::cout << "向Channel发送数据" << std::endl;
        auto result = channel->send(testOp);
        std::cout << "发送结果: " << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
    });
    
    // 从真正的Channel接收数据
    ioManager->scheduler([channel]() {
        std::cout << "从Channel接收数据" << std::endl;
        Op receivedOp;
        auto result = channel->receive(receivedOp, 1000);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "接收成功: " << receivedOp.Operation << " " 
                      << receivedOp.Key << "=" << receivedOp.Value << std::endl;
        } else {
            std::cout << "接收失败或超时" << std::endl;
        }
    });
    
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ioManager->stop();
    std::cout << "兼容层测试完成\n" << std::endl;
}

void testGlobalConfig()
{
    std::cout << "=== 全局配置测试 ===" << std::endl;
    
    std::cout << "默认全局配置:" << std::endl;
    g_migrationConfig.printConfig();
    
    // 修改全局配置
    g_migrationConfig.enableChannel = true;
    g_migrationConfig.defaultChannelCapacity = 100;
    g_migrationConfig.defaultTimeoutMs = 2000;
    
    std::cout << "\n修改后的全局配置:" << std::endl;
    g_migrationConfig.printConfig();
    
    std::cout << "全局配置测试完成\n" << std::endl;
}

int main()
{
    std::cout << "迁移助手工具测试" << std::endl;
    std::cout << "=================" << std::endl;
    
    try {
        testLockQueueWrapper();
        testMigrationFromLockQueue();
        testBenchmark();
        testMigrationConfig();
        testCompatibilityLayer();
        testGlobalConfig();
        
        std::cout << "所有测试完成！" << std::endl;
        std::cout << "\n✅ LockQueue包装器正常工作" << std::endl;
        std::cout << "✅ 数据迁移功能正常" << std::endl;
        std::cout << "✅ 性能基准测试完成" << std::endl;
        std::cout << "✅ 配置管理正常" << std::endl;
        std::cout << "✅ 兼容层正常工作" << std::endl;
        std::cout << "\n🎉 迁移助手工具验证成功！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "测试过程中发生错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
