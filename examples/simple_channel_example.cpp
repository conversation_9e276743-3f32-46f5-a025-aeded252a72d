#include "raft-kv/fiber/monsoon.h"
#include <iostream>
#include <string>
#include <chrono>
#include <thread>

using namespace monsoon;

/**
 * @brief 简单的Channel使用示例
 * @details 使用基本类型演示Channel的功能
 */

// 使用int和string类型演示Channel功能
void demonstrateBasicChannel()
{
    std::cout << "=== 基本Channel演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "BasicChannel");
    ioManager->start();

    // 创建int类型的Channel
    auto intChannel = createChannel<int>(5);

    // 生产者协程
    ioManager->scheduler([intChannel]()
                         {
        for (int i = 1; i <= 10; ++i) {
            auto result = intChannel->send(i);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "发送: " << i << std::endl;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        intChannel->close();
        std::cout << "生产者完成" << std::endl; });

    // 消费者协程
    ioManager->scheduler([intChannel]()
                         {
        while (true) {
            int data;
            auto result = intChannel->receive(data, 1000); // 1秒超时
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收: " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "Channel已关闭，消费者退出" << std::endl;
                break;
            } else if (result == ChannelResult::TIMEOUT) {
                std::cout << "接收超时" << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(2000));
    ioManager->stop();
    std::cout << "基本Channel演示完成\n"
              << std::endl;
}

// 演示字符串Channel
void demonstrateStringChannel()
{
    std::cout << "=== 字符串Channel演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "StringChannel");
    ioManager->start();

    auto stringChannel = createChannel<std::string>(3);

    // 发送方
    ioManager->scheduler([stringChannel]()
                         {
        std::vector<std::string> messages = {
            "Hello", "World", "Channel", "Demo"
        };
        
        for (const auto& msg : messages) {
            auto result = stringChannel->send(msg);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "发送消息: " << msg << std::endl;
            }
        }
        stringChannel->close(); });

    // 接收方
    ioManager->scheduler([stringChannel]()
                         {
        while (true) {
            std::string msg;
            auto result = stringChannel->receive(msg);
            
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收消息: " << msg << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "字符串Channel已关闭" << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "字符串Channel演示完成\n"
              << std::endl;
}

// 演示非阻塞操作
void demonstrateNonBlockingOperations()
{
    std::cout << "=== 非阻塞操作演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(1, true, "NonBlocking");
    ioManager->start();

    auto channel = createChannel<int>(2); // 小缓冲区

    ioManager->scheduler([channel]()
                         {
        // 填满缓冲区
        for (int i = 1; i <= 2; ++i) {
            auto result = channel->trySend(i);
            std::cout << "尝试发送 " << i << ": " 
                      << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        }
        
        // 尝试再发送一个，应该失败
        auto result = channel->trySend(3);
        std::cout << "尝试发送 3: " 
                  << (result == ChannelResult::WOULD_BLOCK ? "缓冲区满" : "意外成功") << std::endl;
        
        // 接收一个数据
        int data;
        result = channel->tryReceive(data);
        if (result == ChannelResult::SUCCESS) {
            std::cout << "非阻塞接收: " << data << std::endl;
        }
        
        // 现在应该可以发送了
        result = channel->trySend(3);
        std::cout << "再次尝试发送 3: " 
                  << (result == ChannelResult::SUCCESS ? "成功" : "失败") << std::endl;
        
        // 接收剩余数据
        while (channel->tryReceive(data) == ChannelResult::SUCCESS) {
            std::cout << "接收: " << data << std::endl;
        }
        
        // 尝试从空Channel接收
        result = channel->tryReceive(data);
        std::cout << "从空Channel接收: " 
                  << (result == ChannelResult::WOULD_BLOCK ? "缓冲区空" : "意外成功") << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ioManager->stop();
    std::cout << "非阻塞操作演示完成\n"
              << std::endl;
}

// 演示超时操作
void demonstrateTimeoutOperations()
{
    std::cout << "=== 超时操作演示 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "Timeout");
    ioManager->start();

    auto channel = createChannel<int>(0); // 无缓冲Channel

    // 超时接收测试
    ioManager->scheduler([channel]()
                         {
        std::cout << "尝试从空Channel接收（500ms超时）..." << std::endl;
        auto start = std::chrono::steady_clock::now();
        
        int data;
        auto result = channel->receive(data, 500);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        std::cout << "接收结果: " 
                  << (result == ChannelResult::TIMEOUT ? "超时" : "成功")
                  << ", 耗时: " << elapsed << "ms" << std::endl; });

    // 超时发送测试
    ioManager->scheduler([channel]()
                         {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        std::cout << "尝试向无缓冲Channel发送（300ms超时）..." << std::endl;
        auto start = std::chrono::steady_clock::now();
        
        auto result = channel->send(42, 300);
        
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        std::cout << "发送结果: " 
                  << (result == ChannelResult::TIMEOUT ? "超时" : "成功")
                  << ", 耗时: " << elapsed << "ms" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    ioManager->stop();
    std::cout << "超时操作演示完成\n"
              << std::endl;
}

// 演示Channel状态查询
void demonstrateChannelStatus()
{
    std::cout << "=== Channel状态查询演示 ===" << std::endl;

    auto channel = createChannel<int>(3);

    std::cout << "初始状态:" << std::endl;
    std::cout << "  容量: " << channel->capacity() << std::endl;
    std::cout << "  大小: " << channel->size() << std::endl;
    std::cout << "  是否空: " << (channel->empty() ? "是" : "否") << std::endl;
    std::cout << "  是否满: " << (channel->full() ? "是" : "否") << std::endl;
    std::cout << "  是否关闭: " << (channel->isClosed() ? "是" : "否") << std::endl;

    // 添加一些数据
    channel->trySend(1);
    channel->trySend(2);

    std::cout << "\n添加2个元素后:" << std::endl;
    std::cout << "  大小: " << channel->size() << std::endl;
    std::cout << "  是否空: " << (channel->empty() ? "是" : "否") << std::endl;
    std::cout << "  是否满: " << (channel->full() ? "是" : "否") << std::endl;

    // 填满Channel
    channel->trySend(3);

    std::cout << "\n填满后:" << std::endl;
    std::cout << "  大小: " << channel->size() << std::endl;
    std::cout << "  是否满: " << (channel->full() ? "是" : "否") << std::endl;

    // 关闭Channel
    channel->close();

    std::cout << "\n关闭后:" << std::endl;
    std::cout << "  是否关闭: " << (channel->isClosed() ? "是" : "否") << std::endl;

    std::cout << "Channel状态查询演示完成\n"
              << std::endl;
}

int main()
{
    std::cout << "简单Channel使用示例" << std::endl;
    std::cout << "==================" << std::endl;

    try
    {
        // 只运行一个演示避免多个IOManager冲突
        demonstrateChannelStatus(); // 这个不需要IOManager

        std::cout << "演示完成！" << std::endl;
        std::cout << "\nChannel的主要优势:" << std::endl;
        std::cout << "1. 类型安全的协程间通信" << std::endl;
        std::cout << "2. 自动的协程调度和唤醒" << std::endl;
        std::cout << "3. 支持超时和非阻塞操作" << std::endl;
        std::cout << "4. 简洁清晰的API" << std::endl;

        // 运行一个需要IOManager的演示
        std::cout << "\n现在运行协程演示..." << std::endl;
        demonstrateBasicChannel();
    }
    catch (const std::exception &e)
    {
        std::cerr << "演示过程中发生错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
