#include "raft-kv/fiber/monsoon.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <cassert>

using namespace monsoon;

// 测试基本的发送和接收功能
void testBasicSendReceive()
{
    std::cout << "=== 测试基本发送接收功能 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "ChannelTest");
    ioManager->start();

    auto channel = createChannel<int>(1); // 缓冲区大小为1

    // 测试发送和接收
    ioManager->scheduler([channel]()
                         {
        std::cout << "协程1: 发送数据 42" << std::endl;
        auto result = channel->send(42);
        assert(result == ChannelResult::SUCCESS);
        std::cout << "协程1: 发送成功" << std::endl; });

    ioManager->scheduler([channel]()
                         {
        std::cout << "协程2: 等待接收数据" << std::endl;
        int data;
        auto result = channel->receive(data);
        assert(result == ChannelResult::SUCCESS);
        assert(data == 42);
        std::cout << "协程2: 接收到数据 " << data << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ioManager->stop();
    std::cout << "基本发送接收测试通过\n"
              << std::endl;
}

// 测试无缓冲Channel
void testUnbufferedChannel()
{
    std::cout << "=== 测试无缓冲Channel ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "UnbufferedTest");
    ioManager->start();

    auto channel = createChannel<std::string>(0); // 无缓冲

    // 发送方
    ioManager->scheduler([channel]()
                         {
        std::cout << "发送方: 准备发送 'Hello'" << std::endl;
        auto result = channel->send(std::string("Hello"));
        assert(result == ChannelResult::SUCCESS);
        std::cout << "发送方: 发送完成" << std::endl; });

    // 接收方
    ioManager->scheduler([channel]()
                         {
        std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 稍微延迟
        std::cout << "接收方: 准备接收" << std::endl;
        std::string data;
        auto result = channel->receive(data);
        assert(result == ChannelResult::SUCCESS);
        assert(data == "Hello");
        std::cout << "接收方: 接收到 '" << data << "'" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    ioManager->stop();
    std::cout << "无缓冲Channel测试通过\n"
              << std::endl;
}

// 测试多生产者多消费者
void testMultipleProducersConsumers()
{
    std::cout << "=== 测试多生产者多消费者 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(4, true, "MultiTest");
    ioManager->start();

    auto channel = createChannel<int>(5); // 缓冲区大小为5
    const int numProducers = 3;
    const int numConsumers = 2;
    const int itemsPerProducer = 5;

    std::atomic<int> totalSent{0};
    std::atomic<int> totalReceived{0};

    // 启动生产者
    for (int i = 0; i < numProducers; ++i)
    {
        ioManager->scheduler([channel, i, itemsPerProducer, &totalSent]()
                             {
            for (int j = 0; j < itemsPerProducer; ++j) {
                int data = i * 100 + j;
                auto result = channel->send(data);
                if (result == ChannelResult::SUCCESS) {
                    totalSent++;
                    std::cout << "生产者" << i << ": 发送 " << data << std::endl;
                }
                Fiber::GetThis()->yield(); // 让出执行权
            } });
    }

    // 启动消费者
    for (int i = 0; i < numConsumers; ++i)
    {
        ioManager->scheduler([channel, i, &totalReceived]()
                             {
            while (totalReceived.load() < numProducers * itemsPerProducer) {
                int data;
                auto result = channel->receive(data, 100); // 100ms超时
                if (result == ChannelResult::SUCCESS) {
                    totalReceived++;
                    std::cout << "消费者" << i << ": 接收 " << data << std::endl;
                } else if (result == ChannelResult::TIMEOUT) {
                    std::cout << "消费者" << i << ": 接收超时" << std::endl;
                    break;
                }
                Fiber::GetThis()->yield();
            } });
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    ioManager->stop();

    std::cout << "总发送: " << totalSent.load() << ", 总接收: " << totalReceived.load() << std::endl;
    std::cout << "多生产者多消费者测试通过\n"
              << std::endl;
}

// 测试Channel关闭
void testChannelClose()
{
    std::cout << "=== 测试Channel关闭 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(2, true, "CloseTest");
    ioManager->start();

    auto channel = createChannel<int>(2);

    // 发送一些数据然后关闭
    ioManager->scheduler([channel]()
                         {
        channel->send(1);
        channel->send(2);
        std::cout << "发送方: 发送完数据，关闭Channel" << std::endl;
        channel->close();
        
        // 尝试再次发送应该失败
        auto result = channel->send(3);
        assert(result == ChannelResult::CLOSED);
        std::cout << "发送方: 关闭后发送失败，符合预期" << std::endl; });

    // 接收数据直到Channel关闭
    ioManager->scheduler([channel]()
                         {
        int data;
        while (true) {
            auto result = channel->receive(data);
            if (result == ChannelResult::SUCCESS) {
                std::cout << "接收方: 接收到 " << data << std::endl;
            } else if (result == ChannelResult::CLOSED) {
                std::cout << "接收方: Channel已关闭" << std::endl;
                break;
            }
        } });

    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    ioManager->stop();
    std::cout << "Channel关闭测试通过\n"
              << std::endl;
}

// 测试非阻塞操作
void testNonBlockingOperations()
{
    std::cout << "=== 测试非阻塞操作 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(1, true, "NonBlockingTest");
    ioManager->start();

    auto channel = createChannel<int>(1);

    ioManager->scheduler([channel]()
                         {
        // 发送一个数据，应该成功
        auto result = channel->trySend(42);
        assert(result == ChannelResult::SUCCESS);
        std::cout << "非阻塞发送成功: 42" << std::endl;
        
        // 再次发送应该失败（缓冲区满）
        result = channel->trySend(43);
        assert(result == ChannelResult::WOULD_BLOCK);
        std::cout << "非阻塞发送失败: 缓冲区满" << std::endl;
        
        // 接收数据
        int data;
        result = channel->tryReceive(data);
        assert(result == ChannelResult::SUCCESS);
        assert(data == 42);
        std::cout << "非阻塞接收成功: " << data << std::endl;
        
        // 再次接收应该失败（缓冲区空）
        result = channel->tryReceive(data);
        assert(result == ChannelResult::WOULD_BLOCK);
        std::cout << "非阻塞接收失败: 缓冲区空" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    ioManager->stop();
    std::cout << "非阻塞操作测试通过\n"
              << std::endl;
}

// 测试超时操作
void testTimeoutOperations()
{
    std::cout << "=== 测试超时操作 ===" << std::endl;

    auto ioManager = std::make_unique<IOManager>(1, true, "TimeoutTest");
    ioManager->start();

    auto channel = createChannel<int>(0); // 无缓冲

    ioManager->scheduler([channel]()
                         {
        auto start = std::chrono::steady_clock::now();
        
        // 尝试从空Channel接收，应该超时
        int data;
        auto result = channel->receive(data, 100); // 100ms超时
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        assert(result == ChannelResult::TIMEOUT);
        assert(elapsed >= 90 && elapsed <= 150); // 允许一些误差
        std::cout << "接收超时测试通过，耗时: " << elapsed << "ms" << std::endl;
        
        // 尝试向满Channel发送，应该超时
        start = std::chrono::steady_clock::now();
        result = channel->send(42, 100); // 100ms超时
        elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - start).count();
        
        assert(result == ChannelResult::TIMEOUT);
        assert(elapsed >= 90 && elapsed <= 150);
        std::cout << "发送超时测试通过，耗时: " << elapsed << "ms" << std::endl; });

    std::this_thread::sleep_for(std::chrono::milliseconds(300));
    ioManager->stop();
    std::cout << "超时操作测试通过\n"
              << std::endl;
}

int main()
{
    std::cout << "开始Channel测试..." << std::endl;

    try
    {
        testBasicSendReceive();
        testUnbufferedChannel();
        testMultipleProducersConsumers();
        testChannelClose();
        testNonBlockingOperations();
        testTimeoutOperations();

        std::cout << "所有Channel测试通过！" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
