#include "fiber/final_channel.h"
#include "fiber/iomanager.h"
#include "raftCore/ApplyMsg.h"
#include "common/util.h"
#include <algorithm>
#include <random>
#include <thread>

namespace monsoon
{
  // 显式实例化常用类型
  template class FinalChannel<int>;
  template class FinalChannel<std::string>;
  template class FinalChannel<ApplyMsg>;
  template class FinalChannel<Op>;
  template class OptimizedChannel<int>;
  template class OptimizedChannel<std::string>;
  template class OptimizedChannel<ApplyMsg>;
  template class OptimizedChannel<Op>;

  template <typename T>
  FinalChannel<T>::FinalChannel(size_t capacity, Scheduler *scheduler)
      : capacity_(capacity), scheduler_(scheduler)
  {
    if (!scheduler_)
    {
      scheduler_ = IOManager::GetThis();
    }
  }

  template <typename T>
  FinalChannel<T>::~FinalChannel()
  {
    close();
  }

  template <typename T>
  ChannelResult FinalChannel<T>::send(const T &data, int timeoutMs)
  {
    return sendImpl(data, timeoutMs, true);
  }

  template <typename T>
  ChannelResult FinalChannel<T>::send(T &&data, int timeoutMs)
  {
    return sendImpl(std::move(data), timeoutMs, true);
  }

  template <typename T>
  ChannelResult FinalChannel<T>::trySend(const T &data)
  {
    return sendImpl(data, 0, false);
  }

  template <typename T>
  ChannelResult FinalChannel<T>::trySend(T &&data)
  {
    return sendImpl(std::move(data), 0, false);
  }

  template <typename T>
  template <typename U>
  ChannelResult FinalChannel<T>::sendImpl(U &&data, int timeoutMs, bool blocking)
  {
    if (closed_.load())
    {
      return ChannelResult::CLOSED;
    }

    auto start = std::chrono::steady_clock::now();

    while (true)
    {
      {
        std::unique_lock<std::mutex> lock(mutex_);

        if (closed_.load())
        {
          return ChannelResult::CLOSED;
        }

        // 如果有等待接收的协程，直接传递数据
        if (!receiveWaiters_.empty())
        {
          auto waiter = receiveWaiters_.front();
          receiveWaiters_.pop();

          // 这里需要实际的数据传递机制
          // 简化实现：直接放入缓冲区，让接收者获取
          buffer_.push(std::forward<U>(data));
          totalSent_++;

          lock.unlock();
          if (scheduler_)
          {
            scheduler_->scheduler(waiter);
          }
          return ChannelResult::SUCCESS;
        }

        // 检查缓冲区是否有空间
        if (buffer_.size() < capacity_)
        {
          buffer_.push(std::forward<U>(data));
          totalSent_++;
          return ChannelResult::SUCCESS;
        }

        // 缓冲区已满
        if (!blocking || timeoutMs == 0)
        {
          return ChannelResult::FULL;
        }

        // 检查超时
        if (timeoutMs > 0 && isTimeout(start, timeoutMs))
        {
          return ChannelResult::TIMEOUT;
        }

        // 等待空间可用
        if (scheduler_)
        {
          auto currentFiber = Fiber::GetThis();
          sendWaiters_.push(currentFiber);
          lock.unlock();

          currentFiber->yield();
          // 被唤醒后继续循环
        }
        else
        {
          // 没有调度器，使用条件变量等待
          std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
      }
    }
  }

  template <typename T>
  ChannelResult FinalChannel<T>::receive(T &data, int timeoutMs)
  {
    if (closed_.load() && buffer_.empty())
    {
      return ChannelResult::CLOSED;
    }

    auto start = std::chrono::steady_clock::now();

    while (true)
    {
      {
        std::unique_lock<std::mutex> lock(mutex_);

        // 检查缓冲区是否有数据
        if (!buffer_.empty())
        {
          data = std::move(buffer_.front());
          buffer_.pop();
          totalReceived_++;

          // 唤醒等待发送的协程
          if (!sendWaiters_.empty())
          {
            auto waiter = sendWaiters_.front();
            sendWaiters_.pop();
            lock.unlock();

            if (scheduler_)
            {
              scheduler_->scheduler(waiter);
            }
          }

          return ChannelResult::SUCCESS;
        }

        // 如果Channel已关闭且缓冲区为空
        if (closed_.load())
        {
          return ChannelResult::CLOSED;
        }

        // 非阻塞模式
        if (timeoutMs == 0)
        {
          return ChannelResult::EMPTY;
        }

        // 检查超时
        if (timeoutMs > 0 && isTimeout(start, timeoutMs))
        {
          return ChannelResult::TIMEOUT;
        }

        // 等待数据可用
        if (scheduler_)
        {
          auto currentFiber = Fiber::GetThis();
          receiveWaiters_.push(currentFiber);
          lock.unlock();

          currentFiber->yield();
          // 被唤醒后继续循环
        }
        else
        {
          // 没有调度器，使用条件变量等待
          std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
      }
    }
  }

  template <typename T>
  ChannelResult FinalChannel<T>::tryReceive(T &data)
  {
    return receive(data, 0);
  }

  template <typename T>
  void FinalChannel<T>::close()
  {
    std::lock_guard<std::mutex> lock(mutex_);
    closed_.store(true);
    wakeupWaiters();
  }

  template <typename T>
  bool FinalChannel<T>::isClosed() const
  {
    return closed_.load();
  }

  template <typename T>
  size_t FinalChannel<T>::size() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    return buffer_.size();
  }

  template <typename T>
  bool FinalChannel<T>::empty() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    return buffer_.empty();
  }

  template <typename T>
  bool FinalChannel<T>::full() const
  {
    std::lock_guard<std::mutex> lock(mutex_);
    return buffer_.size() >= capacity_;
  }

  template <typename T>
  void FinalChannel<T>::wakeupWaiters()
  {
    // 唤醒所有等待的协程
    while (!sendWaiters_.empty())
    {
      auto waiter = sendWaiters_.front();
      sendWaiters_.pop();
      if (scheduler_)
      {
        scheduler_->scheduler(waiter);
      }
    }

    while (!receiveWaiters_.empty())
    {
      auto waiter = receiveWaiters_.front();
      receiveWaiters_.pop();
      if (scheduler_)
      {
        scheduler_->scheduler(waiter);
      }
    }
  }

  template <typename T>
  bool FinalChannel<T>::isTimeout(const std::chrono::steady_clock::time_point &start, int timeoutMs) const
  {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start).count();
    return elapsed >= timeoutMs;
  }

  // ChannelSelector实现
  template <typename T>
  void FinalChannelSelector::addSendCase(typename FinalChannel<T>::ptr channel, const T &data,
                                         std::function<void(bool)> callback)
  {
    SelectCase selectCase;
    selectCase.type = OperationType::SEND;
    selectCase.channel = channel.get();
    selectCase.data = const_cast<T *>(&data);
    selectCase.tryOperation = [channel, data]() -> bool
    {
      return channel->trySend(data) == ChannelResult::SUCCESS;
    };
    selectCase.callback = [callback]()
    { if (callback) callback(true); };
    cases_.push_back(selectCase);
  }

  template <typename T>
  void FinalChannelSelector::addReceiveCase(typename FinalChannel<T>::ptr channel, T *data,
                                            std::function<void(const T &, bool)> callback)
  {
    SelectCase selectCase;
    selectCase.type = OperationType::RECEIVE;
    selectCase.channel = channel.get();
    selectCase.data = data;
    selectCase.tryOperation = [channel, data]() -> bool
    {
      return channel->tryReceive(*data) == ChannelResult::SUCCESS;
    };
    selectCase.callback = [callback, data]()
    { if (callback) callback(*data, true); };
    cases_.push_back(selectCase);
  }

  void FinalChannelSelector::addDefaultCase(std::function<void()> callback)
  {
    SelectCase selectCase;
    selectCase.type = OperationType::DEFAULT;
    selectCase.channel = nullptr;
    selectCase.data = nullptr;
    selectCase.tryOperation = []() -> bool
    { return true; };
    selectCase.callback = callback;
    cases_.push_back(selectCase);
  }

  FinalChannelSelector::SelectResult FinalChannelSelector::select(int timeoutMs)
  {
    SelectResult result;

    // 首先尝试非阻塞操作
    int caseIndex = tryExecuteNonBlocking();
    if (caseIndex >= 0)
    {
      result.caseIndex = caseIndex;
      result.success = true;
      result.result = ChannelResult::SUCCESS;
      if (cases_[caseIndex].callback)
      {
        cases_[caseIndex].callback();
      }
      return result;
    }

    // 如果有默认case且没有其他操作成功，执行默认case
    for (size_t i = 0; i < cases_.size(); ++i)
    {
      if (cases_[i].type == OperationType::DEFAULT)
      {
        result.caseIndex = static_cast<int>(i);
        result.success = true;
        result.result = ChannelResult::SUCCESS;
        if (cases_[i].callback)
        {
          cases_[i].callback();
        }
        return result;
      }
    }

    // 如果没有默认case，等待或超时
    auto start = std::chrono::steady_clock::now();
    while (timeoutMs < 0 || !isTimeout(start, timeoutMs))
    {
      std::this_thread::sleep_for(std::chrono::milliseconds(1));

      caseIndex = tryExecuteNonBlocking();
      if (caseIndex >= 0)
      {
        result.caseIndex = caseIndex;
        result.success = true;
        result.result = ChannelResult::SUCCESS;
        if (cases_[caseIndex].callback)
        {
          cases_[caseIndex].callback();
        }
        return result;
      }
    }

    // 超时
    result.success = false;
    result.result = ChannelResult::TIMEOUT;
    result.errorMessage = "Select operation timed out";
    return result;
  }

  int FinalChannelSelector::tryExecuteNonBlocking()
  {
    // 随机化case顺序，避免饥饿
    std::vector<size_t> indices;
    for (size_t i = 0; i < cases_.size(); ++i)
    {
      if (cases_[i].type != OperationType::DEFAULT)
      {
        indices.push_back(i);
      }
    }

    if (!indices.empty())
    {
      auto now = std::chrono::steady_clock::now();
      size_t seed = now.time_since_epoch().count();
      std::shuffle(indices.begin(), indices.end(), std::default_random_engine(seed));
    }

    for (size_t idx : indices)
    {
      if (cases_[idx].tryOperation && cases_[idx].tryOperation())
      {
        return static_cast<int>(idx);
      }
    }

    return -1; // 没有可执行的操作
  }

  void FinalChannelSelector::clear()
  {
    cases_.clear();
  }

  bool FinalChannelSelector::isTimeout(const std::chrono::steady_clock::time_point &start, int timeoutMs) const
  {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - start).count();
    return elapsed >= timeoutMs;
  }

} // namespace monsoon
